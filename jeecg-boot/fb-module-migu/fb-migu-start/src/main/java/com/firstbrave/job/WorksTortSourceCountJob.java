package com.firstbrave.job;

import cn.hutool.core.collection.CollUtil;
import com.firstbrave.enums.DataSource;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.entity.FbmWorks;
import org.jeecg.entity.po.InfringementClues;
import org.jeecg.modules.platform.mapper.WorksTortSourceCountMapper;
import org.jeecg.modules.platform.service.impl.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.util.*;

import static cn.hutool.core.collection.ListUtil.partition;

/**
 * @Author: ggr
 * @CreateTime: 2025-09-05
 * @Description: 作品侵权来源数量统计任务
 * @Version: 1.0
 */

@Component
@Slf4j
public class WorksTortSourceCountJob {

    @Resource
    private WorksTortSourceCountMapper worksTortSourceCountMapper;
    @Resource
    private FbmTortDataVideoServiceImpl fbmTortDataVideoService;
    @Resource
    private FbmTortDataAudioServiceImpl fbmTortDataAudioService;
    @Resource
    private FbmTortDataTxtServiceImpl fbmTortDataTxtService;
    @Resource
    private FbmTortDataPicServiceImpl fbmTortDataPicService;
    @Resource
    private FbmTortDataIptvServiceImpl fbmTortDataIptvService;
    @Resource
    private FbmWorksServiceImpl fbmWorksService;

    @XxlJob("WorksTortSourceCount")
    public ReturnT<String> execute(String param) {
        try {
            log.info("开始执行作品侵权来源数量统计任务");

            // 清空原有统计数据
            worksTortSourceCountMapper.delete(null);

            // 获取所有作品
            List<FbmWorks> worksList = fbmWorksService.list();
            if (CollUtil.isEmpty(worksList)) {
                log.info("没有找到作品数据");
                return ReturnT.SUCCESS;
            }

            // 分批处理作品
            List<List<FbmWorks>> partitions = partition(worksList, 100);

            for (List<FbmWorks> worksPartition : partitions) {
                processSingleWorks(worksPartition);
            }

            log.info("作品侵权来源数量统计任务执行完成");
            return ReturnT.SUCCESS;

        } catch (Exception e) {
            log.error("作品侵权来源数量统计任务执行失败", e);
            return ReturnT.FAIL;
        }
    }


    /**
     * 处理单个作品的侵权来源统计
     */
    private void processSingleWorks(List<FbmWorks> fbmWorksList) {
        // 收集各数据来源的侵权数据
        List<InfringementClues> cluesList = new ArrayList<>();

        // 根据作品类型统计不同类型的侵权数据
        String firstCategory = works.getFirstCategory();
        if (firstCategory != null) {
            switch (firstCategory) {
                case "1": // 视频
                    collectVideoTortData(worksIds, cluesList);
                    break;
                case "2": // 音频
                    collectAudioTortData(worksIds, cluesList);
                    break;
                case "3": // 文字
                    collectTxtTortData(worksIds, cluesList);
                    break;
                case "4": // 图片
                    collectPicTortData(worksIds, cluesList);
                    break;
                default:
                    break;
            }
        }

        // 计算占比并保存统计结果
        if (!cluesList.isEmpty()) {
            calculateRatesAndSave(worksId, cluesList);
        }
    }

    /**
     * 收集视频侵权数据
     */
    private void collectVideoTortData(List<Long> worksIds, List<InfringementClues> cluesList) {
        try {
            // 使用 Mapper 方法查询视频侵权数据按数据来源分组
            List<Map<String, Object>> videoResults = fbmTortDataVideoService.getBaseMapper().getCountByDataSource(worksIds);

            for (Map<String, Object> result : videoResults) {
                InfringementClues clue = new InfringementClues();
                clue.setTortSource(getDataSourceName(result.get("tort_data_source")));
                clue.setTortCount(Long.valueOf(result.get("count").toString()));
                cluesList.add(clue);
            }
        } catch (Exception e) {
            log.error("收集视频侵权数据失败", e);
        }
    }

    /**
     * 收集音频侵权数据
     */
    private void collectAudioTortData(List<Long> worksIds, List<InfringementClues> cluesList) {
        try {
            // 使用 Mapper 方法查询音频侵权数据按数据来源分组
            List<Map<String, Object>> audioResults = fbmTortDataAudioService.getBaseMapper().getCountByDataSource(worksIds);

            for (Map<String, Object> result : audioResults) {
                InfringementClues clue = new InfringementClues();
                clue.setTortSource(getDataSourceName(result.get("data_source")));
                clue.setTortCount(Long.valueOf(result.get("count").toString()));
                cluesList.add(clue);
            }
        } catch (Exception e) {
            log.error("收集音频侵权数据失败", e);
        }
    }

    /**
     * 收集文字侵权数据
     */
    private void collectTxtTortData(List<Long> worksIds, List<InfringementClues> cluesList) {
        try {
            // 使用 Mapper 方法查询文字侵权数据按数据来源分组
            List<Map<String, Object>> txtResults = fbmTortDataTxtService.getBaseMapper().getCountByDataSource(worksIds);

            for (Map<String, Object> result : txtResults) {
                InfringementClues clue = new InfringementClues();
                clue.setTortSource( getDataSourceName(result.get("tort_data_source")));
                clue.setTortCount(Long.valueOf(result.get("count").toString()));
                cluesList.add(clue);
            }
        } catch (Exception e) {
            log.error("收集文字侵权数据失败", e);
        }
    }

    /**
     * 收集图片侵权数据
     */
    private void collectPicTortData(List<Long> worksIds, List<InfringementClues> cluesList) {
        try {
            // 使用 Mapper 方法查询图片侵权数据按数据来源分组
            List<Map<String, Object>> picResults = fbmTortDataPicService.getBaseMapper().getCountByDataSource(worksIds);

            for (Map<String, Object> result : picResults) {
                InfringementClues clue = new InfringementClues();
                clue.setTortSource(getDataSourceName(result.get("tort_data_source")));
                clue.setTortCount(Long.valueOf(result.get("count").toString()));
                cluesList.add(clue);
            }
        } catch (Exception e) {
            log.error("收集图片侵权数据失败", e);
        }
    }

    /**
     * 收集IPTV侵权数据
     */
    private void collectIptvTortData(List<Long> worksIds, List<InfringementClues> cluesList) {
        try {
            // IPTV表没有数据来源字段，统一归类为IPTV来源
            Map<String, Long> iptvCount = fbmTortDataIptvService.getCountNum(worksIds);
            Long count = iptvCount.get("tortCount");

            if (count != null && count > 0) {
                InfringementClues clue = new InfringementClues();
                clue.setTortSource("IPTV");
                clue.setTortCount(count);
                cluesList.add(clue);
            }
        } catch (Exception e) {
            log.error("收集IPTV侵权数据失败", e);
        }
    }

    /**
     * 获取数据来源名称
     */
    private String getDataSourceName(Object dataSource) {
        if (dataSource == null) {
            return "未知来源";
        }

        String sourceStr = dataSource.toString();
        return DataSource.getDataSourceByCode(sourceStr).getName();

    }

    /**
     * 计算占比并保存统计结果
     */
    private void calculateRatesAndSave(Long worksId, List<InfringementClues> cluesList) {
        try {
            // 计算总数
            long totalCount = cluesList.stream()
                    .mapToLong(InfringementClues::getTortCount)
                    .sum();

            if (totalCount == 0) {
                return;
            }

            // 计算占比并保存
            DecimalFormat df = new DecimalFormat("#.##");

            for (InfringementClues clue : cluesList) {
                // 计算占比
                double rate = (double) clue.getTortCount() / totalCount * 100;
                clue.setTortCountRate(rate);

                // 保存到数据库
                org.jeecg.entity.po.WorksTortSourceCount record = new org.jeecg.entity.po.WorksTortSourceCount();
                record.setWorksId(worksId.intValue());
                record.setTortSource(clue.getTortSource());
                record.setTortCount(clue.getTortCount().toString());
                record.setRate(df.format(rate) + "%");

                worksTortSourceCountMapper.insert(record);
            }

            log.debug("作品{}侵权来源统计完成，共{}个来源，总计{}条侵权数据",
                     worksId, cluesList.size(), totalCount);

        } catch (Exception e) {
            log.error("计算作品{}侵权来源占比失败", worksId, e);
        }
    }
}
