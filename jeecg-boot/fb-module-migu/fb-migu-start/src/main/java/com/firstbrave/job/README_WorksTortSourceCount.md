# 作品侵权来源数量统计任务 (WorksTortSourceCount)

## 功能描述

该任务实现了通过作品ID查询侵权数据，按照数据来源进行分组统计，并计算出各分组的占比。

## 主要功能

1. **数据收集**: 从多个侵权数据表中收集数据
   - 视频侵权表 (fbm_tort_data_video)
   - 音频侵权表 (fbm_tort_data_audio) 
   - 文字侵权表 (fbm_tort_data_txt)
   - 图片侵权表 (fbm_tort_data_pic)
   - IPTV侵权表 (fbm_tort_data_iptv)

2. **数据分组**: 按照数据来源字段进行分组统计
   - 视频/文字/图片: `tort_data_source` 字段
   - 音频: `data_source` 字段
   - IPTV: 统一归类为"IPTV"来源

3. **占比计算**: 计算每个数据来源的侵权数据占比

4. **结果存储**: 将统计结果保存到 `works_tort_source_count` 表

## 数据来源映射

- 1: 爬虫监测
- 2: 人工举报  
- 3: 第三方接口
- 4: 系统自动检测
- 其他: 来源{数字}

## 执行流程

1. 清空原有统计数据
2. 获取所有作品列表
3. 分批处理作品（每批100个）
4. **按 FirstCategory 分组处理**：
   - 将作品按 FirstCategory 字段分组
   - 记录每个类别的作品数量
5. 对每个类别中的每个作品：
   - 根据作品类型收集对应的侵权数据
   - 按数据来源分组统计
   - 计算各来源占比
   - 保存统计结果到数据库

## 分组逻辑

### FirstCategory 分组
- **"1"**: 视频类作品 - 收集视频侵权数据 + IPTV侵权数据
- **"2"**: 音频类作品 - 收集音频侵权数据
- **"3"**: 文字类作品 - 收集文字侵权数据
- **"4"**: 图片类作品 - 收集图片侵权数据
- **"unknown"**: 未知类型 - 收集所有类型的侵权数据

### 处理优势
- **性能优化**: 按类别分组减少不必要的数据查询
- **数据准确性**: 每个作品单独统计确保数据精确
- **日志追踪**: 详细记录每个类别的处理情况

## 新增的 Mapper 方法

为支持按数据来源分组统计，在各个侵权数据 Mapper 中新增了 `getCountByDataSource` 方法：

- `FbmTortDataVideoMapper.getCountByDataSource()`
- `FbmTortDataAudioMapper.getCountByDataSource()`
- `FbmTortDataTxtMapper.getCountByDataSource()`
- `FbmTortDataPicMapper.getCountByDataSource()`

## 使用方式

该任务通过 XXL-Job 调度执行，任务名称为 "WorksTortSourceCount"。

## 注意事项

1. 任务执行前会清空 `works_tort_source_count` 表的所有数据
2. 只统计有效的侵权数据 (tort_is_del = 1)
3. 占比计算保留两位小数
4. 支持按作品类型进行针对性的数据收集
5. 异常处理确保单个作品处理失败不影响整体任务执行
