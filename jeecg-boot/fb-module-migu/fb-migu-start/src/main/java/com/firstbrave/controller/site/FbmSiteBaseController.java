package com.firstbrave.controller.site;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.entity.param.FbmSiteBaseAddParam;
import org.jeecg.entity.param.FbmSiteBaseEditParam;
import org.jeecg.entity.param.FbmSiteBaseExcelParam;
import org.jeecg.entity.param.FbmSiteBasePageParam;
import org.jeecg.entity.po.site.FbmSiteBase;
import org.jeecg.entity.po.site.FbmSiteContact;
import org.jeecg.entity.po.site.FbmSiteIcp;
import org.jeecg.entity.vo.FbmSitePageVo;
import org.jeecg.modules.platform.service.IFbmSiteBaseService;
import org.jeecg.modules.platform.service.IFbmSiteContactService;
import org.jeecg.modules.platform.service.IFbmSiteIcpService;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.enmus.ExcelType;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @Description: 侵权站点
 * @Author: fbi
 * @Date: 2025-09-01
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "侵权站点表")
@RestController
@RequestMapping("/iLoginController/fbmSiteBase")
public class FbmSiteBaseController extends JeecgController<FbmSiteBase, IFbmSiteBaseService> {
    @Resource
    private IFbmSiteBaseService fbmSiteBaseService;
    @Resource
    private IFbmSiteIcpService fbmSiteIcpService;
    @Resource
    private IFbmSiteContactService fbmSiteContactService;


    /**
     * 分页列表查询
     *
     * @param fbmSiteBasePageParam
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "侵权站点表-分页列表查询")
    @ApiOperation(value = "侵权站点表-分页列表查询", notes = "侵权站点表-分页列表查询")
    @PostMapping(value = "/list")
    public Result<IPage<FbmSitePageVo>> queryPageList(@RequestBody FbmSiteBasePageParam fbmSiteBasePageParam,
                                                      @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                      @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                      HttpServletRequest req) {
        Page<FbmSiteBasePageParam> page = new Page<>(pageNo, pageSize);
        IPage<FbmSitePageVo> pageList = fbmSiteBaseService.pageSiteAndContact(page, fbmSiteBasePageParam);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param fbmSiteBaseAddParam
     * @return
     */
    @AutoLog(value = "侵权站点表-添加")
    @ApiOperation(value = "侵权站点表-添加", notes = "侵权站点表-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody FbmSiteBaseAddParam fbmSiteBaseAddParam) {
        String s = fbmSiteBaseService.saveFbmSiteIcpContact(fbmSiteBaseAddParam);
        return Result.OK(s);
    }

    /**
     * 编辑
     *
     * @param fbmSiteBaseEditParam
     * @return
     */
    @AutoLog(value = "侵权站点表-编辑")
    @ApiOperation(value = "侵权站点表-编辑", notes = "侵权站点表-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody FbmSiteBaseEditParam fbmSiteBaseEditParam) {
        fbmSiteBaseService.updateSiteIcpContact(fbmSiteBaseEditParam);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "侵权站点表-通过id删除")
    @ApiOperation(value = "侵权站点表-通过id删除", notes = "侵权站点表-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {

        fbmSiteBaseService.removeSiteContactIcp(Long.parseLong(id));
        return Result.OK("删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "侵权站点表-通过id查询")
    @ApiOperation(value = "侵权站点表-通过id查询", notes = "侵权站点表-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<FbmSiteBaseEditParam> queryById(@RequestParam(name = "id", required = true) String id) {
        FbmSiteBaseEditParam fbmSiteBaseEditParam = new FbmSiteBaseEditParam();
        FbmSiteBase fbmSiteBase = fbmSiteBaseService.getOne(new QueryWrapper<FbmSiteBase>().eq("id",
                id).eq("is_enabled", 1));
        FbmSiteIcp fbmSiteIcp = fbmSiteIcpService.getOne(new QueryWrapper<FbmSiteIcp>().eq("base_id", id));
        FbmSiteContact fbmSiteContact = fbmSiteContactService.getOne(
                new QueryWrapper<FbmSiteContact>().eq("site_base_id",
                        id).eq("status", 1));
        if (fbmSiteBase == null) {
            return Result.error("没有此id");
        } else {
            fbmSiteBaseEditParam.setId(Long.parseLong(id));
            fbmSiteBaseEditParam.setHost(fbmSiteBase.getHost());
            fbmSiteBaseEditParam.setSiteShowName(fbmSiteBase.getSiteShowName());
            fbmSiteBaseEditParam.setDomain(fbmSiteBase.getDomain());
            fbmSiteBaseEditParam.setSiteType(fbmSiteBase.getSiteType());
            fbmSiteBaseEditParam.setContentFirstCategory(fbmSiteBase.getContentFirstCategory());
            fbmSiteBaseEditParam.setContentSecondCategory(fbmSiteBase.getContentSecondCategory());
        }
        if (fbmSiteIcp == null) {
            fbmSiteBaseEditParam.setIcpOrgName(null);
            fbmSiteBaseEditParam.setIcpNum(null);
            fbmSiteBaseEditParam.setIcpOrgNature(null);
            fbmSiteBaseEditParam.setDomainServer(null);
        } else {
            fbmSiteBaseEditParam.setIcpOrgName(fbmSiteIcp.getIcpOrgName());
            fbmSiteBaseEditParam.setIcpNum(fbmSiteIcp.getIcpNum());
            fbmSiteBaseEditParam.setIcpOrgNature(fbmSiteIcp.getIcpOrgNature());
            fbmSiteBaseEditParam.setDomainServer(fbmSiteIcp.getDomainServer());
        }
        if (fbmSiteContact == null) {
            fbmSiteBaseEditParam.setContactName(null);
            fbmSiteBaseEditParam.setContactEmail(null);
            fbmSiteBaseEditParam.setContactMobile(null);
        } else {
            fbmSiteBaseEditParam.setContactName(fbmSiteContact.getContactName());
            fbmSiteBaseEditParam.setContactEmail(fbmSiteContact.getContactEmail());
            fbmSiteBaseEditParam.setContactMobile(fbmSiteContact.getContactMobile());
        }

        return Result.OK(fbmSiteBaseEditParam);
    }

    /**
     * @Description:导出数据
     * @Author: ggr
     * @date:
     */
    @AutoLog(value = "侵权站点表-导出数据")
    @ApiOperation(value = "侵权站点表-导出数据", notes = "侵权站点表-导出数据")
    @GetMapping(value = "/exportXls")
    public ModelAndView exportXls(
            @RequestParam(name = "siteShowName", required = false) String siteShowName,
            @RequestParam(name = "domain", required = false) String domain,
            @RequestParam(name = "host", required = false) String host,
            @RequestParam(name = "contactName", required = false) String contactName,
            @RequestParam(name = "contactEmail", required = false) String contactEmail,
            @RequestParam(name = "contactMobile", required = false) String contactMobile) {
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "站点管理数据导出报表");
        mv.addObject(NormalExcelConstants.CLASS, FbmSiteBaseExcelParam.class);
        ExportParams exportParams = new ExportParams();
        exportParams.setType(ExcelType.XSSF);
        mv.addObject(NormalExcelConstants.PARAMS, exportParams);
        FbmSiteBasePageParam fbmSiteBasePageParam = new FbmSiteBasePageParam();
        fbmSiteBasePageParam.setSiteShowName(siteShowName);
        fbmSiteBasePageParam.setDomain(domain);
        fbmSiteBasePageParam.setHost(host);
        fbmSiteBasePageParam.setContactName(contactName);
        fbmSiteBasePageParam.setContactEmail(contactEmail);
        fbmSiteBasePageParam.setContactMobile(contactMobile);

        List<FbmSiteBaseExcelParam> list = fbmSiteBaseService.listSiteExcel(fbmSiteBasePageParam);
        mv.addObject(NormalExcelConstants.DATA_LIST, list);
        return mv;

    }


}
