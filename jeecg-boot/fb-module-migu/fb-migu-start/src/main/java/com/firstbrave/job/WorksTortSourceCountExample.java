package com.firstbrave.job;

import org.jeecg.entity.FbmWorks;
import java.util.*;
import java.util.stream.Collectors;

/**
 * WorksTortSourceCount 分组功能示例
 * 
 * @Author: ggr
 * @CreateTime: 2025-09-05
 * @Description: 演示按 FirstCategory 分组的逻辑
 * @Version: 1.0
 */
public class WorksTortSourceCountExample {
    
    /**
     * 演示分组逻辑的示例方法
     */
    public static void demonstrateGrouping() {
        // 模拟作品数据
        List<FbmWorks> worksList = createSampleWorks();
        
        System.out.println("=== 原始作品列表 ===");
        worksList.forEach(works -> 
            System.out.println("作品ID: " + works.getId() + ", 类别: " + works.getFirstCategory())
        );
        
        // 按 FirstCategory 分组
        Map<String, List<FbmWorks>> categoryWorksMap = worksList.stream()
                .collect(Collectors.groupingBy(works -> 
                    works.getFirstCategory() != null ? works.getFirstCategory() : "unknown"));
        
        System.out.println("\n=== 按类别分组结果 ===");
        categoryWorksMap.forEach((category, works) -> {
            System.out.println("类别 " + category + " (" + getCategoryName(category) + "):");
            works.forEach(w -> System.out.println("  - 作品ID: " + w.getId()));
            System.out.println("  共 " + works.size() + " 个作品\n");
        });
        
        // 演示处理逻辑
        System.out.println("=== 处理逻辑演示 ===");
        categoryWorksMap.forEach((category, works) -> {
            System.out.println("处理类别: " + category + " (" + getCategoryName(category) + ")");
            System.out.println("将收集的数据类型: " + getDataTypesToCollect(category));
            System.out.println("作品数量: " + works.size());
            System.out.println();
        });
    }
    
    /**
     * 创建示例作品数据
     */
    private static List<FbmWorks> createSampleWorks() {
        List<FbmWorks> works = new ArrayList<>();
        
        // 视频类作品
        works.add(createWorks(1001L, "1"));
        works.add(createWorks(1002L, "1"));
        works.add(createWorks(1003L, "1"));
        
        // 音频类作品
        works.add(createWorks(2001L, "2"));
        works.add(createWorks(2002L, "2"));
        
        // 文字类作品
        works.add(createWorks(3001L, "3"));
        works.add(createWorks(3002L, "3"));
        works.add(createWorks(3003L, "3"));
        works.add(createWorks(3004L, "3"));
        
        // 图片类作品
        works.add(createWorks(4001L, "4"));
        
        // 未知类型作品
        works.add(createWorks(5001L, null));
        works.add(createWorks(5002L, ""));
        
        return works;
    }
    
    /**
     * 创建作品对象
     */
    private static FbmWorks createWorks(Long id, String firstCategory) {
        FbmWorks works = new FbmWorks();
        works.setId(id);
        works.setFirstCategory(firstCategory);
        return works;
    }
    
    /**
     * 获取类别名称
     */
    private static String getCategoryName(String category) {
        switch (category) {
            case "1": return "视频";
            case "2": return "音频";
            case "3": return "文字";
            case "4": return "图片";
            case "unknown": return "未知类型";
            default: return "其他";
        }
    }
    
    /**
     * 获取需要收集的数据类型
     */
    private static String getDataTypesToCollect(String category) {
        switch (category) {
            case "1": return "视频侵权数据 + IPTV侵权数据";
            case "2": return "音频侵权数据";
            case "3": return "文字侵权数据";
            case "4": return "图片侵权数据";
            case "unknown": return "所有类型侵权数据";
            default: return "未定义";
        }
    }
    
    /**
     * 主方法 - 运行演示
     */
    public static void main(String[] args) {
        demonstrateGrouping();
    }
}
