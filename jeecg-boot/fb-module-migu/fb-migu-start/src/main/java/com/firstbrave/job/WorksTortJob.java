package com.firstbrave.job;

import cn.hutool.core.collection.CollUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.entity.FbmWorks;
import org.jeecg.entity.po.WorksDetailTortCount;
import org.jeecg.modules.platform.mapper.WorksDetailTortCountMapper;
import org.jeecg.modules.platform.mapper.WorksTortSourceCountMapper;
import org.jeecg.modules.platform.service.impl.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import static cn.hutool.core.collection.ListUtil.partition;

/**
 * @Author: ggr
 * @CreateTime: 2025-09-05
 * @Description:作品侵权线索信息
 * @Version: 1.0
 */

@Component
@Slf4j
public class WorksTortJob {
    @Resource
    private WorksTortSourceCountMapper worksTortSourceCountMapper;
    @Resource
    private WorksDetailTortCountMapper worksdetailTortCountMapper;
    @Resource
    private FbmTortDataVideoServiceImpl fbmTortDataVideoService;
    @Resource
    private FbmTortDataAudioServiceImpl fbmTortDataAudioService;
    @Resource
    private FbmTortDataTxtServiceImpl fbmTortDataTxtService;
    @Resource
    private FbmTortDataPicServiceImpl fbmTortDataPicService;
    @Resource
    private FbmTortDataIptvServiceImpl fbmTortDataIptvService;
    @Resource
    private FbmWorksServiceImpl fbmWorksService;

    @XxlJob("WorksTortJob")
    public ReturnT<String> execute(String param) {
        List<FbmWorks> list = fbmWorksService.list();
        List<List<FbmWorks>> partition = partition(list, 100);
        worksdetailTortCountMapper.delete(null);
        partition.forEach(fbmWorksList -> {
            if (CollUtil.isNotEmpty(fbmWorksList)) {
                fbmWorksList.forEach(
                        fbmWorks -> {
                            //有效线索量
                            Long tortValidCount = 0L;
                            //无效线索量
                            Long tortInvalidCount = 0L;
                            switch (fbmWorks.getFirstCategory()) {
                                case "1":
                                    //有效线索量
                                    Map<String, Long> countNum = fbmTortDataVideoService.getCountNum(
                                            CollUtil.newArrayList(fbmWorks.getId()));
                                    tortValidCount = countNum.get("tortCount");
                                    Map<String, Long> countNum1 =
                                            fbmTortDataIptvService.getCountNum(CollUtil.newArrayList(fbmWorks.getId()));
                                    tortValidCount = countNum1.get("tortCount");
                                    //无效线索量
                                    Map<String, Long> countNumValid = fbmTortDataVideoService.getCountNumValid(
                                            CollUtil.newArrayList(fbmWorks.getId()));
                                    tortInvalidCount = countNumValid.get("tortCount");
                                    Map<String, Long> countNumValid1 = fbmTortDataIptvService.getCountNumValid(
                                            CollUtil.newArrayList(fbmWorks.getId()));
                                    tortInvalidCount = countNumValid1.get("tortCount");

                                    break;
                                case "2":
                                    //有效线索量
                                    Map<String, Long> countNum2 = fbmTortDataAudioService.getCountNum(
                                            CollUtil.newArrayList(fbmWorks.getId()));
                                    tortValidCount = countNum2.get("tortCount");
                                    //无效线索量
                                    Map<String, Long> countNumValid2 = fbmTortDataAudioService.getCountNumValid(
                                            CollUtil.newArrayList(fbmWorks.getId()));
                                    tortInvalidCount = countNumValid2.get("tortCount");
                                    break;
                                case "3":
                                    Map<String, Long> countNum3 = fbmTortDataTxtService.getCountNum(
                                            CollUtil.newArrayList(fbmWorks.getId()));
                                    tortValidCount = countNum3.get("tortCount");
                                    Map<String, Long> countNumValid3 = fbmTortDataTxtService.getCountNumValid(
                                            CollUtil.newArrayList(fbmWorks.getId()));
                                    tortInvalidCount = countNumValid3.get("tortCount");
                                    break;
                                case "4":
                                    Map<String, Long> countNum4 = fbmTortDataPicService.getCountNum(
                                            CollUtil.newArrayList(fbmWorks.getId()));
                                    tortValidCount = countNum4.get("tortCount");
                                    Map<String, Long> countNumValid4 = fbmTortDataPicService.getCountNumValid(
                                            CollUtil.newArrayList(fbmWorks.getId()));
                                    tortInvalidCount = countNumValid4.get("tortCount");
                                    break;
                                default:
                                    break;
                            }
                            WorksDetailTortCount worksDetailTortCount = new WorksDetailTortCount();
                            worksDetailTortCount.setWorksId(fbmWorks.getId());
                            long l = tortValidCount + tortInvalidCount;
                            worksDetailTortCount.setTortCount(Long.toString(l));
                            worksDetailTortCount.setTortValiCount(tortValidCount.toString());
                            worksDetailTortCount.setTortInvalidCount(tortInvalidCount.toString());
                            int insert = worksdetailTortCountMapper.insert(
                                    worksDetailTortCount);
                        });
            }
        });
        return ReturnT.SUCCESS;


    }
}
