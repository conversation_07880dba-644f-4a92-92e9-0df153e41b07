package com.firstbrave.controller.white;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.PageUtil;
import org.jeecg.entity.FbmCustomerWorksAttachment;
import org.jeecg.entity.FbmWorks;
import org.jeecg.entity.param.FbmMiguWhiteAddParam;
import org.jeecg.entity.param.FbmMiguWhiteEditParam;
import org.jeecg.entity.po.FbmMiguWhitePageParam;
import org.jeecg.entity.po.customer.FbmCustomer;
import org.jeecg.entity.po.white.FbmMiguWhite;
import org.jeecg.entity.po.white.FbmMiguWhiteWorks;
import org.jeecg.entity.vo.FbmMiguWhiteVo;
import org.jeecg.entity.vo.FbmWorksVo;
import org.jeecg.modules.platform.service.IFbmMiguWhiteService;
import org.jeecg.modules.platform.service.IFbmMiguWhiteWorksService;
import org.jeecg.modules.platform.service.IFbmWorksService;
import org.jeecg.modules.platform.service.impl.FbmCustomerServiceImpl;
import org.jeecg.modules.platform.service.impl.FbmCustomerWorksAttachmentServiceImpl;
import org.jeecg.modules.platform.service.impl.FbmWorksServiceImpl;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.enmus.ExcelType;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 白名单管理
 * @Author: ggr
 * @Date: 2025-08-29
 */
@Slf4j
@Api(tags = "白名单管理")
@RestController
@RequestMapping("/fbmMiguWhite")
public class FbmMiguWhiteController extends JeecgController<FbmMiguWhite, IFbmMiguWhiteService> {
    @Resource
    private IFbmMiguWhiteService fbmMiguWhiteService;
    @Resource
    private IFbmMiguWhiteWorksService iFbmMiguWhiteWorksService;
    @Resource
    private IFbmWorksService fbmWorksService;
    @Resource
    private FbmCustomerWorksAttachmentServiceImpl fbmCustomerWorksAttachmentService;
    @Resource
    private FbmCustomerServiceImpl fbmCustomerService;


    /**
     * 分页列表查询
     *
     * @param fbmMiguWhitePageParam
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "白名单管理-分页列表查询")
    @ApiOperation(value = "白名单管理-分页列表查询", notes = "白名单管理-分页列表查询")
    @PostMapping(value = "/list")
    public Result<Page<FbmMiguWhiteVo>> queryPageList(@RequestBody FbmMiguWhitePageParam fbmMiguWhitePageParam,
                                                      @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                      @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                      HttpServletRequest req) {
        FbmMiguWhite fbmMiguWhite = BeanUtil.copyProperties(fbmMiguWhitePageParam, FbmMiguWhite.class);
        QueryWrapper<FbmMiguWhite> queryWrapper = QueryGenerator.initQueryWrapper(fbmMiguWhite, req.getParameterMap());
        Page<FbmMiguWhite> page = new Page<FbmMiguWhite>(pageNo, pageSize);
        IPage<FbmMiguWhite> pageList = fbmMiguWhiteService.page(page, queryWrapper);
        List<FbmMiguWhite> records = pageList.getRecords();
        List<Long> whiteIds = records.stream().map(FbmMiguWhite::getId).collect(Collectors.toList());
        Page<FbmMiguWhiteVo> generate = PageUtil.generate(pageList, FbmMiguWhiteVo.class);
        List<FbmMiguWhiteVo> fbmMiguWhiteVos = generate.getRecords();
        if (CollUtil.isNotEmpty(whiteIds) && CollUtil.isNotEmpty(fbmMiguWhiteVos)) {
            setWorksNumber(whiteIds, fbmMiguWhiteVos);
        } else {
            fbmMiguWhiteVos.forEach(
                    s -> s.setWorksNumber(0L)
            );
        }
        return Result.OK(generate);
    }

    private void setWorksNumber(List<Long> whiteIds, List<FbmMiguWhiteVo> fbmMiguWhiteVos) {
        // 执行分组统计查询
        List<Map<String, Object>> stats = iFbmMiguWhiteWorksService.listMaps(
                new QueryWrapper<FbmMiguWhiteWorks>()
                        .in("migu_white_id", whiteIds)
                        .groupBy("migu_white_id")
                        .select("migu_white_id", "count(works_id) as works_count")
        );
        // 转换为 Map<String, Long>（migu_job_id -> works_count）
        Map<String, Long> maps = stats.stream()
                .collect(Collectors.toMap(
                        map -> map.get("migu_white_id").toString(),
                        map -> ((Number) map.get("works_count")).longValue()
                ));
        //设置作品数
        fbmMiguWhiteVos.forEach(
                s -> s.setWorksNumber(maps.getOrDefault(s.getId().toString(), 0L))
        );
    }

    /**
     * 添加
     *
     * @param fbmMiguWhiteAddParam
     * @return
     */
    @AutoLog(value = "白名单管理-添加")
    @ApiOperation(value = "白名单管理-添加", notes = "白名单管理-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody FbmMiguWhiteAddParam fbmMiguWhiteAddParam) {
        FbmMiguWhite fbmMiguWhite = new FbmMiguWhite();
        BeanUtil.copyProperties(fbmMiguWhiteAddParam, fbmMiguWhite);
        fbmMiguWhiteService.save(fbmMiguWhite);
        Long id = fbmMiguWhite.getId();
        List<FbmMiguWhiteWorks> fbmMiguWhiteWorksList = CollUtil.newArrayList();
        if (CollUtil.isNotEmpty(fbmMiguWhiteAddParam.getWorksIdList())) {
            fbmMiguWhiteAddParam.getWorksIdList().forEach(
                    s -> {
                        FbmMiguWhiteWorks fbmMiguWhiteWorks = new FbmMiguWhiteWorks();
                        fbmMiguWhiteWorks.setMiguWhiteId(id);
                        fbmMiguWhiteWorks.setWorksId(s.toString());
                        fbmMiguWhiteWorksList.add(fbmMiguWhiteWorks);
                    }
            );
        }
        iFbmMiguWhiteWorksService.saveBatch(fbmMiguWhiteWorksList);
        return Result.OK("添加成功！");
    }


    /**
     * 编辑
     *
     * @param fbmMiguWhiteEditParam
     * @return
     */
    @AutoLog(value = "白名单管理-编辑")
    @ApiOperation(value = "白名单管理-编辑", notes = "白名单管理-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody FbmMiguWhiteEditParam fbmMiguWhiteEditParam) {
        FbmMiguWhite fbmMiguWhite = new FbmMiguWhite();
        BeanUtil.copyProperties(fbmMiguWhiteEditParam, fbmMiguWhite);
        LambdaQueryWrapper<FbmMiguWhite> eq = new LambdaQueryWrapper<FbmMiguWhite>().eq(FbmMiguWhite::getId,
                fbmMiguWhiteEditParam.getId());
        fbmMiguWhiteService.update(fbmMiguWhite, eq);
        Long id = fbmMiguWhiteEditParam.getId();
        List<FbmMiguWhiteWorks> fbmMiguWhiteWorksList = CollUtil.newArrayList();
        if (CollUtil.isNotEmpty(fbmMiguWhiteEditParam.getWorksIdList())) {
            fbmMiguWhiteEditParam.getWorksIdList().forEach(
                    s -> {
                        FbmMiguWhiteWorks fbmMiguWhiteWorks = new FbmMiguWhiteWorks();
                        fbmMiguWhiteWorks.setMiguWhiteId(id);
                        fbmMiguWhiteWorks.setWorksId(s.toString());
                        fbmMiguWhiteWorksList.add(fbmMiguWhiteWorks);
                    }
            );
        }
        iFbmMiguWhiteWorksService.remove(
                new LambdaQueryWrapper<FbmMiguWhiteWorks>().eq(FbmMiguWhiteWorks::getMiguWhiteId, id)
        );
        iFbmMiguWhiteWorksService.saveBatch(fbmMiguWhiteWorksList);
        return Result.OK("编辑成功!");
    }
    @AutoLog(value = "白名单管理-通过白名单id查询白名单以及作品数据")
    @ApiOperation(value = "白名单管理-通过白名单id查询白名单以及作品数据", notes = "白名单管理-通过白名单id查询白名单以及作品数据")
    @PostMapping(value = "/queryWhiteAndWorksListByWhiteId")
    public Result<FbmMiguWhiteEditParam> queryWhiteAndWorksListByWhiteId(@RequestParam(name = "id", required = true) String miguWhiteId) {
        FbmMiguWhiteEditParam fbmMiguWhiteEditParam = new FbmMiguWhiteEditParam();
        FbmMiguWhite fbmMiguWhite = fbmMiguWhiteService.getById(miguWhiteId);
        BeanUtil.copyProperties(fbmMiguWhite, fbmMiguWhiteEditParam);
        if (fbmMiguWhite == null) {
            return Result.error("没有此白名单id");
        }
        List<FbmMiguWhiteWorks> miguJobId = iFbmMiguWhiteWorksService.list(
                new QueryWrapper<FbmMiguWhiteWorks>().eq("migu_white_id", miguWhiteId));
        List<Long> worksIdLongs = miguJobId.stream()
                .map(FbmMiguWhiteWorks::getWorksId)
                .map(Long::parseLong)
                .collect(Collectors.toList());
        List<FbmWorks> fbmWorks = fbmWorksService.listByIds(worksIdLongs);
        ArrayList<Map<String, String>> worksList = new ArrayList<>();
        fbmWorks.forEach(fbmWorks1 -> {
            Map<String, String> map = new HashMap<>();
            map.put("worksId", fbmWorks1.getId().toString());
            map.put("worksName", fbmWorks1.getWorksName());
            worksList.add(map);
        });
        if (CollUtil.isEmpty(worksIdLongs)) {
            fbmMiguWhiteEditParam.setWorksList(CollUtil.newArrayList());
        }else {
            fbmMiguWhiteEditParam.setWorksList(worksList);
        }
        return Result.OK(fbmMiguWhiteEditParam);
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "白名单管理-批量删除")
    @ApiOperation(value = "白名单管理-批量删除", notes = "白名单管理-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.fbmMiguWhiteService.removeByIds(Arrays.asList(ids.split(",")));
        iFbmMiguWhiteWorksService.remove(
                new LambdaQueryWrapper<FbmMiguWhiteWorks>().in(FbmMiguWhiteWorks::getMiguWhiteId,
                        Arrays.asList(ids.split(",")))
        );
        return Result.OK("批量删除成功！");
    }

    /**
     * 通过白名单id查询作品
     *
     * @return
     */
    @AutoLog(value = "白名单管理-通过白名单id查询作品列表")
    @ApiOperation(value = "白名单管理-通过白名单id查询作品列表", notes = "白名单管理通过白名单id查询作品列表")
    @PostMapping(value = "/queryWorksListByWhiteId")
    public Result<IPage<FbmWorksVo>> queryWorksListByWhiteId(
            @RequestParam(name = "id", required = true) String miguWhiteId,
            @RequestParam(name = "worksName", required = false) String worksName,
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        List<FbmMiguWhiteWorks> miguJobId = iFbmMiguWhiteWorksService.list(
                new QueryWrapper<FbmMiguWhiteWorks>().eq("migu_white_id", miguWhiteId));
        List<Long> worksIdLongs = miguJobId.stream()
                .map(FbmMiguWhiteWorks::getWorksId)
                .map(Long::parseLong)
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(worksIdLongs)) {
            return Result.error("没有此白名单id");
        }
        Page<FbmWorks> fbmWorksPage = new Page<FbmWorks>(pageNo, pageSize);
        IPage<FbmWorks> page = fbmWorksService.page(fbmWorksPage,
                new QueryWrapper<FbmWorks>().in("id", worksIdLongs));
        if (StrUtil.isNotEmpty(worksName)) {
            page = fbmWorksService.page(fbmWorksPage,
                    new QueryWrapper<FbmWorks>().in("id", worksIdLongs).like("works_name", worksName));
        } else {
            page = fbmWorksService.page(fbmWorksPage, new QueryWrapper<FbmWorks>().in("id", worksIdLongs));
        }
        ArrayList<FbmWorksVo> objects = new ArrayList<>();
        for (FbmWorks s : page.getRecords()) {
            FbmWorksVo fbmWorksVo = BeanUtil.copyProperties(s, FbmWorksVo.class);
            //查询客户名称
            Wrapper<FbmCustomerWorksAttachment> wrapper = new LambdaQueryWrapper<FbmCustomerWorksAttachment>().eq(
                    FbmCustomerWorksAttachment::getWorksId,
                    s.getId());
            FbmCustomerWorksAttachment one = fbmCustomerWorksAttachmentService.getOne(wrapper);
            if (one != null) {
                Integer customerId = one.getCustomerId();
                FbmCustomer customer = fbmCustomerService.getById(customerId);
                if (customer != null) {
                    fbmWorksVo.setCustomerName(customer.getCustomerName());
                }
            }
            //作品场次
            ObjectMapper mapper = new ObjectMapper();
            try {
                // 1. 解析 JSON 字符串
                JsonNode rootNode = mapper.readTree(s.getWorksInfo());
                // 2. 获取 "对阵" 的值
                String duiZhen = rootNode.get("对阵").asText();
                fbmWorksVo.setScene(duiZhen);
            } catch (Exception e) {
                e.printStackTrace();
                fbmWorksVo.setScene("");
            }
            //重点关注平台
            List<String> strings = JSON.parseArray(s.getSite(), String.class);
            fbmWorksVo.setSiteList(strings);
            objects.add(fbmWorksVo);
        }
        Page<FbmWorksVo> generate = PageUtil.generate(page, FbmWorksVo.class);
        generate.setRecords(objects);
        return Result.OK(generate);
    }

    /**
     * @Description: 导出白名单数据
     * @Author: ggr
     */
    @AutoLog(value = "白名单管理-导出数据")
    @ApiOperation(value = "白名单管理-导出数据", notes = "白名单管理-导出数据")
    @GetMapping(value = "/exportXls")
    public ModelAndView exportXls() {
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "白名单管理数据导出报表");
        mv.addObject(NormalExcelConstants.CLASS, FbmMiguWhiteVo.class);
        ExportParams exportParams = new ExportParams();
        exportParams.setType(ExcelType.XSSF);
        mv.addObject(NormalExcelConstants.PARAMS, exportParams);
        List<FbmMiguWhite> list = fbmMiguWhiteService.list();
        List<FbmMiguWhiteVo> fbmMiguWhiteVos = BeanUtil.copyToList(list, FbmMiguWhiteVo.class);
        List<Long> collect = list.stream().map(FbmMiguWhite::getId).collect(Collectors.toList());
        setWorksNumber(collect, fbmMiguWhiteVos);
        mv.addObject(NormalExcelConstants.DATA_LIST, fbmMiguWhiteVos);
        return mv;
    }

    /**
     * @Description:导出作品数据
     * @Author: ggr
     */
    @AutoLog(value = "白名单管理-导出作品数据")
    @ApiOperation(value = "白名单管理-导出作品数据", notes = "白名单管理-导出作品数据")
    @GetMapping(value = "/exportWorksXls")
    public ModelAndView exportWorksXls(@RequestParam(name = "id", required = false) String id,
                                       @RequestParam(name = "worksName", required = false) String worksName) {
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "白名单管理作品数据导出报表");
        mv.addObject(NormalExcelConstants.CLASS, FbmWorks.class);
        ExportParams exportParams = new ExportParams();
        exportParams.setType(ExcelType.XSSF);
        mv.addObject(NormalExcelConstants.PARAMS, exportParams);
        List<FbmMiguWhiteWorks> miguJobId = iFbmMiguWhiteWorksService.list(
                new QueryWrapper<FbmMiguWhiteWorks>().eq("migu_white_id", id));
        List<String> worksIds = miguJobId.stream().map(FbmMiguWhiteWorks::getWorksId).collect(Collectors.toList());
        if (CollUtil.isEmpty(worksIds)) {
            return mv;
        }
        List<FbmWorks> list;
        if (StrUtil.isNotEmpty(worksName)) {
            list = fbmWorksService.list(
                    new QueryWrapper<FbmWorks>().in("id", worksIds).like("works_name", worksName));
        } else {
            list = fbmWorksService.list(
                    new QueryWrapper<FbmWorks>().in("id", worksIds));
        }
        ArrayList<FbmWorksVo> objects = new ArrayList<>();
        for (FbmWorks s : list) {
            FbmWorksVo fbmWorksVo = BeanUtil.copyProperties(s, FbmWorksVo.class);
            //查询客户名称
            Wrapper<FbmCustomerWorksAttachment> wrapper = new LambdaQueryWrapper<FbmCustomerWorksAttachment>().eq(
                    FbmCustomerWorksAttachment::getWorksId,
                    s.getId());
            FbmCustomerWorksAttachment one = fbmCustomerWorksAttachmentService.getOne(wrapper);
            if (one != null) {
                Integer customerId = one.getCustomerId();
                FbmCustomer customer = fbmCustomerService.getById(customerId);
                if (customer != null) {
                    fbmWorksVo.setCustomerName(customer.getCustomerName());
                }
            }
            //作品场次
            ObjectMapper mapper = new ObjectMapper();
            try {
                // 1. 解析 JSON 字符串
                JsonNode rootNode = mapper.readTree(s.getWorksInfo());
                // 2. 获取 "对阵" 的值
                String duiZhen = rootNode.get("对阵").asText();
                fbmWorksVo.setScene(duiZhen);
            } catch (Exception e) {
                e.printStackTrace();
                fbmWorksVo.setScene("");
            }
            //重点关注平台
            List<String> strings = JSON.parseArray(s.getSite(), String.class);
            fbmWorksVo.setSiteList(strings);
            objects.add(fbmWorksVo);
        }
        mv.addObject(NormalExcelConstants.CLASS, FbmWorksVo.class);
        mv.addObject(NormalExcelConstants.DATA_LIST, objects);
        return mv;
    }

    @AutoLog(value = "白名单管理-查询作品数据")
    @ApiOperation(value = "白名单管理-查询作品数据", notes = "白名单管理-查询作品数据")
    @GetMapping(value = "/pageWorks")
    public Result<?> pageWorks(@RequestParam(name = "worksName", required = false) String worksName,
                               @RequestParam(name = "pageNo", required = false, defaultValue = "1") Integer pageNo,
                               @RequestParam(name = "pageSize", required = false, defaultValue = "10") Integer pageSize
    ) {
        QueryWrapper<FbmWorks> wrapper = new QueryWrapper<FbmWorks>().like("works_name", worksName);
        Page<FbmWorks> page = new Page<>(pageNo, pageSize);
        Page<FbmWorks> page1 = fbmWorksService.page(page, wrapper);
        Page<FbmWhiteWorksVo> generate = PageUtil.generate(page1, FbmWhiteWorksVo.class);
        return Result.ok(generate);
    }

    @Data
    public static class FbmWhiteWorksVo {
        @ApiModelProperty("作品名称")
        private String worksName;
        @ApiModelProperty("作品id")
        private String id;
        @ApiModelProperty("作者")
        private String author;
    }


}
