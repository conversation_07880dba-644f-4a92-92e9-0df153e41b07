package com.firstbrave.enums;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 入库数据来源
 *
 * <AUTHOR>
 * @date 2019/4/23
 */
public enum DataSource {

    /**
     * 工作台手动上传
     */
    WORKBENCH("工作台", 0),

    /**
     * 云平台Excel上传
     */
    CLOUD_PLATFORM("云平台Excel", 1),

    /**
     * 浏览器插件上传
     */
    BROWSER("浏览器插件", 3),

    /**
     * 汪哥Excel上传
     */
    WANG_GE("汪哥Excel", 4),


    /**
     * 人工查找漏招数据:eqain走疑似侵权数据过来的数据
     */
    MANUAL_LZ("人工查找漏招数据", 9),

    /**
     * 汪哥Excel上传
     */
    WANG_GE_SAW("汪哥疑似侵权Excel", 41),





    /**
     * 在线审核网盘数据
     */
    ONLINE_NETDISC("网盘", 5),

    /**
     * 在线审核视频网站数据
     */
    ONLINE_VIDEO("视频网站", 6),

    /**
     * 在线审核小网站数据
     */
    ONLINE_SMALL_WEBSITE("小网站", 7),

    /**
     * 在线审核社交数据
     */
    ONLINE_SOCIAL("社交", 8),

    /**
     * 体育直播数据
     */
    LIVE_SPORTS("体育直播", 11),


    /**
     * 文字在线审核
     */
    ONLINE_TXT("文字在线审核", 21),







    /**
     * 舆情
     */
    LYRICAL("舆情", 201),

    /**
     * 头条接口
     */
    INTERFACE_HEADLINE("头条接口", 202),

    /**
     * 头条漏招
     */
    TTLZ("头条漏招", 204),

    /**
     * 中视频漏召
     */
    TTLZ2("头条漏招", 205),

    //206好像是易犬漏召
    LZ("漏招数据", 206),


    MG_READING_LZ("咪咕阅读漏招数据", 207),

    WANG_LZ("汪晴川比对漏召-视频", 208),



    YOUKU("易犬优酷接口", 301),

    EQAIN("易犬数据", 302),

    EQAIN2("易犬举报数据", 303),



    NEW_PLATFORM_CHECK("新工作台采集数据审核", 311),

    SITE_VIDEO_SOURCE("视频源网站", 312),

    MANUAL_BIND_SOURCE("手动绑定作品数据", 313),

    HONGGUO_FEISHU("红果专项短剧漏召-飞书", 314),

    COPY("复制其它作品", 316),

    MONITOR_TXT("文字监测", 401),

    SYNCHRO_TXT("文字同步", 402);







    private String name;

    private int code;

    DataSource(String name, int code) {
        this.name = name;
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public int getCode() {
        return code;
    }


    public static DataSource getDataSourceByCode(String code) {
        for (DataSource ac : DataSource.values()) {
            if (Integer.valueOf(ac.getCode()).equals(Integer.valueOf(code))) {
                return ac;
            }
        }
        return null;
    }



}