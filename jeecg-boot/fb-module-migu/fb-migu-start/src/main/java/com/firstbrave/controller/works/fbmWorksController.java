package com.firstbrave.controller.works;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.util.PageUtil;
import org.jeecg.entity.FbmCustomerWorksAttachment;
import org.jeecg.entity.FbmWorks;
import org.jeecg.entity.po.FbmAttachment;
import org.jeecg.entity.po.InfringementClues;
import org.jeecg.entity.po.PowerMaterial;
import org.jeecg.entity.po.customer.FbmCustomer;
import org.jeecg.entity.po.white.FbmMiguWhite;
import org.jeecg.entity.po.white.FbmMiguWhiteWorks;
import org.jeecg.entity.vo.FbmWorksPageVo;
import org.jeecg.entity.vo.WorksDetailVo;
import org.jeecg.modules.platform.service.*;
import org.jeecg.modules.platform.service.impl.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 作品
 * @Author: fbi
 * @Date: 2025-09-04
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "作品")
@RestController
@RequestMapping("/fbmWorks")
public class fbmWorksController extends JeecgController<FbmWorks, IFbmWorksService> {
    @Resource
    FbmTortDataAudioServiceImpl fbmTortDataAudioService;
    @Resource
    FbmTortDataTxtServiceImpl fbmTortDataTxtService;
    @Resource
    FbmTortDataPicServiceImpl fbmTortDataPicService;
    @Resource
    FbmTortDataIptvServiceImpl fbmTortDataIptvService;
    @Resource
    FbmTortDataVideoServiceImpl fbmTortDataVideoService;
    @Resource
    private IFbmWorksService fbmWorksService;
    @Resource
    private FbmCustomerWorksAttachmentServiceImpl fbmCustomerWorksAttachmentService;
    @Resource
    private FbmCustomerServiceImpl fbmCustomerService;
    @Resource
    private IFbmMiguWhiteService fbmMiguWhiteService;
    @Resource
    private IFbmMiguWhiteWorksService iFbmMiguWhiteWorksService;
    @Resource
    private IFbmCustomerWorksAttachmentService iFbmCustomerWorksAttachmentService;
    @Resource
    private IFbmAttachmentService iFbmAttachmentService;
    ;
    @Autowired
    private FbmAttachmentServiceImpl fbmAttachmentServiceImpl;

    /**
     * 分页列表查询
     *
     * @param worksName
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "作品-分页列表查询")
    @ApiOperation(value = "作品-分页列表查询", notes = "作品-分页列表查询")
    @PostMapping(value = "/list")
    public Result<?> queryPageList(@RequestParam String worksName,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<FbmWorks> queryWrapper = new QueryWrapper<FbmWorks>().like("works_name", worksName);
        Page<FbmWorks> page = new Page<FbmWorks>(pageNo, pageSize);
        IPage<FbmWorks> pageList = fbmWorksService.page(page, queryWrapper);
        ArrayList<FbmWorksPageVo> objects = new ArrayList<>();
        pageList.getRecords().forEach(
                s -> {
                    FbmWorksPageVo fbmWorksVo = new FbmWorksPageVo();
                    //作品场次
                    ObjectMapper mapper = new ObjectMapper();
                    try {
                        JsonNode rootNode = mapper.readTree(s.getWorksInfo());
                        String actors = rootNode.get("actors").asText();
                        fbmWorksVo.setActors(actors);
                        String director = rootNode.get("director").asText();
                        fbmWorksVo.setDirector(director);
                    } catch (Exception e) {
                        e.printStackTrace();
                        fbmWorksVo.setActors("");
                        fbmWorksVo.setDirector("");
                    }
                    fbmWorksVo.setFirstCategory(s.getFirstCategory());
                    fbmWorksVo.setSecondCategory(s.getSecondCategory());
                    fbmWorksVo.setId(s.getId());
                    fbmWorksVo.setWorksName(s.getWorksName());
                    Long tortCount = 0L;
                    //通过作品id得到侵权数量
                    switch (s.getFirstCategory()) {
                        case "1":
                            Map<String, Long> countNum = fbmTortDataVideoService.getCountNum(
                                    CollUtil.newArrayList(s.getId()));
                            tortCount += countNum.get("tortCount");
                            Map<String, Long> countNum1 =
                                    fbmTortDataIptvService.getCountNum(CollUtil.newArrayList(s.getId()));
                            tortCount += countNum1.get("tortCount");
                            break;
                        case "2":
                            Map<String, Long> countNum2 = fbmTortDataAudioService.getCountNum(
                                    CollUtil.newArrayList(s.getId()));
                            tortCount += countNum2.get("tortCount");
                            break;
                        case "3":
                            Map<String, Long> countNum3 = fbmTortDataTxtService.getCountNum(
                                    CollUtil.newArrayList(s.getId()));
                            tortCount += countNum3.get("tortCount");
                            break;
                        case "4":
                            Map<String, Long> countNum4 = fbmTortDataPicService.getCountNum(
                                    CollUtil.newArrayList(s.getId()));
                            tortCount += countNum4.get("tortCount");
                            break;
                        default:
                            break;
                    }
                    fbmWorksVo.setTortCount(tortCount);
                    objects.add(fbmWorksVo);

                }
        );
        Page<FbmWorksPageVo> generate = PageUtil.generate(pageList, FbmWorksPageVo.class);
        generate.setRecords(objects);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param fbmWorks
     * @return
     */
    @AutoLog(value = "作品-添加")
    @ApiOperation(value = "作品-添加", notes = "作品-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody FbmWorks fbmWorks) {
        fbmWorksService.save(fbmWorks);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param fbmWorks
     * @return
     */
    @AutoLog(value = "作品-编辑")
    @ApiOperation(value = "作品-编辑", notes = "作品-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody FbmWorks fbmWorks) {
        fbmWorksService.updateById(fbmWorks);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "作品-通过id删除")
    @ApiOperation(value = "作品-通过id删除", notes = "作品-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        fbmWorksService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "作品-批量删除")
    @ApiOperation(value = "作品-批量删除", notes = "作品-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.fbmWorksService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功！");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "作品-通过id查询")
    @ApiOperation(value = "作品-通过id查询", notes = "作品-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<WorksDetailVo> queryById(@RequestParam(name = "id", required = true) String id) {
        FbmWorks fbmWorks = fbmWorksService.getById(id);
        WorksDetailVo worksDetailVo = new WorksDetailVo();
        //作品场次
        ObjectMapper mapper = new ObjectMapper();
        try {
            JsonNode rootNode = mapper.readTree(fbmWorks.getWorksInfo());
            String actors = rootNode.get("actors").asText();
            worksDetailVo.setActors(actors);
            String director = rootNode.get("director").asText();
            worksDetailVo.setDirector(director);
        } catch (Exception e) {
            e.printStackTrace();
            worksDetailVo.setActors("");
            worksDetailVo.setDirector("");
        }
        //查询客户名称
        Wrapper<FbmCustomerWorksAttachment> wrapper = new LambdaQueryWrapper<FbmCustomerWorksAttachment>().eq(
                FbmCustomerWorksAttachment::getWorksId,
                fbmWorks.getId());
        FbmCustomerWorksAttachment one = fbmCustomerWorksAttachmentService.getOne(wrapper);
        if (one != null) {
            Integer customerId = one.getCustomerId();
            FbmCustomer customer = fbmCustomerService.getById(customerId);
            if (customer != null) {
                worksDetailVo.setCustomerName(customer.getCustomerName());
            }
        }
        //重点关注平台
        List<String> strings = JSON.parseArray(fbmWorks.getSite(), String.class);
        worksDetailVo.setSiteList(strings);
        //有效线索量
        Long tortValidCount = 0L;
        //无效线索量
        Long tortInvalidCount = 0L;
        switch (fbmWorks.getFirstCategory()) {
            case "1":
                //有效线索量
                Map<String, Long> countNum = fbmTortDataVideoService.getCountNum(
                        CollUtil.newArrayList(fbmWorks.getId()));
                tortValidCount += countNum.get("tortCount");
                Map<String, Long> countNum1 =
                        fbmTortDataIptvService.getCountNum(CollUtil.newArrayList(fbmWorks.getId()));
                tortValidCount += countNum1.get("tortCount");
                //无效线索量
                Map<String, Long> countNumValid = fbmTortDataVideoService.getCountNumValid(
                        CollUtil.newArrayList(fbmWorks.getId()));
                tortInvalidCount += countNumValid.get("tortCount");
                Map<String, Long> countNumValid1 = fbmTortDataIptvService.getCountNumValid(
                        CollUtil.newArrayList(fbmWorks.getId()));
                tortInvalidCount += countNumValid1.get("tortCount");

                break;
            case "2":
                //有效线索量
                Map<String, Long> countNum2 = fbmTortDataAudioService.getCountNum(
                        CollUtil.newArrayList(fbmWorks.getId()));
                tortValidCount += countNum2.get("tortCount");
                //无效线索量
                Map<String, Long> countNumValid2 = fbmTortDataAudioService.getCountNumValid(
                        CollUtil.newArrayList(fbmWorks.getId()));
                tortInvalidCount += countNumValid2.get("tortCount");
                break;
            case "3":
                Map<String, Long> countNum3 = fbmTortDataTxtService.getCountNum(
                        CollUtil.newArrayList(fbmWorks.getId()));
                tortValidCount += countNum3.get("tortCount");
                Map<String, Long> countNumValid3 = fbmTortDataTxtService.getCountNumValid(
                        CollUtil.newArrayList(fbmWorks.getId()));
                tortInvalidCount += countNumValid3.get("tortCount");
                break;
            case "4":
                Map<String, Long> countNum4 = fbmTortDataPicService.getCountNum(
                        CollUtil.newArrayList(fbmWorks.getId()));
                tortValidCount += countNum4.get("tortCount");
                Map<String, Long> countNumValid4 = fbmTortDataPicService.getCountNumValid(
                        CollUtil.newArrayList(fbmWorks.getId()));
                tortInvalidCount += countNumValid4.get("tortCount");
                break;
            default:
                break;
        }
        worksDetailVo.setValidTortCount(tortValidCount);
        worksDetailVo.setInvalidTortCount(tortInvalidCount);
        worksDetailVo.setTortCountAll(tortValidCount + tortInvalidCount);
/*        //侵权线索信息按照数据来源分类
        switch (fbmWorks.getFirstCategory()) {
            case "1":
                new Thread(() -> {
                    List<InfringementClues> infringementClues = fbmTortDataVideoService.groupByTortSource(
                            CollUtil.newArrayList(fbmWorks.getId()));
                    worksDetailVo.setInfringementClues(infringementClues);
                }).start();
                break;
            case "2":

                break;
            case "3":
                break;
            case "4":
                break;
            default:
                break;
        }
        worksDetailVo.setInfringementClues(infringementClues);*/
        //权力材料信息
        List<PowerMaterial> powerMaterial = new ArrayList<>();
        List<FbmCustomerWorksAttachment> attachmentList = iFbmCustomerWorksAttachmentService.list(
                new QueryWrapper<FbmCustomerWorksAttachment>().eq("works_id", id));
        Set<Integer> collect = attachmentList.stream().map(FbmCustomerWorksAttachment::getAttachmentId).collect(
                Collectors.toSet());
        List<FbmAttachment> fbmAttachmentList = fbmAttachmentServiceImpl.list(
                new QueryWrapper<FbmAttachment>().in("id", collect));
        fbmAttachmentList.forEach(
                s -> {
                    PowerMaterial powerMaterial1 = new PowerMaterial();
                    powerMaterial1.setFileName(s.getAttachmentName());
                    powerMaterial1.setFilePath(s.getAttachmentPath());
                    powerMaterial1.setFileType(s.getAttachmentType());
                    powerMaterial.add(powerMaterial1);
                }
        );
        worksDetailVo.setPowerMaterial(powerMaterial);
        //白名单信息
        List<FbmMiguWhite> miguWhite = new ArrayList<>();
        List<FbmMiguWhiteWorks> fbmMiguWhiteWorksList = iFbmMiguWhiteWorksService.list(
                new QueryWrapper<FbmMiguWhiteWorks>().eq("works_id", id));
        Set<Long> miguWhiteIds = fbmMiguWhiteWorksList.stream()
                .map(FbmMiguWhiteWorks::getMiguWhiteId)
                .collect(Collectors.toSet());
        miguWhiteIds.forEach(
                s -> {
                    FbmMiguWhite fbmMiguWhite = fbmMiguWhiteService.getById(s);
                    miguWhite.add(fbmMiguWhite);
                }
        );
        worksDetailVo.setMiguWhite(miguWhite);

        return Result.OK();
    }

}
