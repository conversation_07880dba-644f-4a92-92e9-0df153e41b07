package com.firstbrave.migu.dna.mapper;

import com.firstbrave.migu.dna.pojo.MgCopyrightLvideoDownloadInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface MgCopyrightLvideoDownloadInfoMapper {

	@Select("select distinct mcldi.* " +
			"from mg_copyright_lvideo_download_info mcldi " +
			"inner join mg_copyright_lvideo_list mcll on mcll.url_md5 = mcldi.url_md5 " +
			"inner join mg_copyright_lvideo_base mclb on mclb.url_md5 = mcll.base_url_md5 " +
			"where mcldi.status=0 and mcldi.is_whole=1 and mclb.source in (" +
			"'11','12','13','14','15','16','17','18'," +
			"'21','22','23','24','25','26','27','28'," +
			"'31','32','33','34','35','36','37','38'," +
			"'51','52','53','54','55','56','57','58'," +
			"'61','62','63','64','65','66','67','68'," +
			"'91','92','93','94','95','96','97','98'" +
			") " +
			"and mclb.is_free in ('1','2','3') and mcldi.source=1 " +
			"limit ${limit}")
	List<MgCopyrightLvideoDownloadInfo> listBsNotExtracted(@Param("limit") int limit);

	@Select("select mcldi.* " +
			"from mg_copyright_lvideo_download_info mcldi " +
			"inner join mg_copyright_high_video_list mchvil on mcldi.file_md5 = mchvil.dna_md5 " +
			"where mcldi.status=0 and is_whole=1 " +
			"limit ${limit}")
	List<MgCopyrightLvideoDownloadInfo> listHighNotExtracted(int limit);

	@Select("select * " +
			"from mg_copyright_lvideo_download_info " +
			"where status=0 and is_whole=1 and source=0 " +
			"limit ${limit}")
	List<MgCopyrightLvideoDownloadInfo> listLsNotExtracted(@Param("limit") int limit);

	@Select("select * " +
			"from mg_copyright_lvideo_download_info " +
			"where status=0 and is_whole=1 and source=2 " +
			"limit ${limit}")
	List<MgCopyrightLvideoDownloadInfo> listWangNotExtracted(@Param("limit") int limit);


	@Update("update mg_copyright_lvideo_download_info set status=#{status}, error_info=#{errorInfo} where id = #{id}")
	void updateErrorStatus(@Param("id") Long id, @Param("status") String status, @Param("errorInfo") String errorInfo);

	@Update("update mg_copyright_lvideo_download_info set status=#{status}, dna_file_size=#{dnaFileSize}, file_dna_path=#{dnaPath} where id = #{id}")
	void updateStatusAndDnaPath(@Param("id") Long id, @Param("status") String status, @Param("dnaFileSize") Long dnaFileSize, @Param("dnaPath") String dnaPath);

	@Update("<script>" +
			"update mg_copyright_lvideo_download_info set status='4' where id in " +
			"<foreach collection=\"ids\" item=\"item\" separator=\",\" open=\"(\" close=\")\">" +
			"	#{item} " +
			"</foreach>" +
			"</script>")
	void updateExtracting(@Param("ids") List<Long> ids);

	@Update("update mg_copyright_lvideo_download_info set status='0' where status = '4'")
	void updateExtractingToInit();

	@Select("select mcll.id, mcll.base_url_md5, mcll.url_md5, mcldi.is_whole " +
			"from mg_copyright_lvideo_list mcll " +
			"left join mg_copyright_lvideo_download_info mcldi on mcll.url_md5 = mcldi.url_md5 " +
			"where mcll.base_url_md5 = #{baseUrlMd5}")
	List<MgCopyrightLvideoDownloadInfo> listByBaseUrlMd5(@Param("baseUrlMd5") String baseUrlMd5);
}
