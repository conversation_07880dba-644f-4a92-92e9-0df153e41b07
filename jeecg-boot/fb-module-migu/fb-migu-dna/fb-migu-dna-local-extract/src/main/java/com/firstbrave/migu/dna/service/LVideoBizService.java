package com.firstbrave.migu.dna.service;

import com.firstbrave.migu.dna.mapper.MgCopyrightLvideoDownloadInfoMapper;
import com.firstbrave.migu.dna.pojo.MgCopyrightLvideoDownloadInfo;
import com.firstbrave.migu.dna.queue.AutoLoadingQueue;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class LVideoBizService {

	@Value("${migu.dna.local.root-path}")
	private String dnaRootPath;
	@Value("${migu.dna.local.concurrency}")
	private int concurrency;
	@Value("${migu.dna.local.extract-img-path}")
	private String extractImgPath;
	@Value("${migu.dna.local.extract-img-h265-path}")
	private String extractImgH265Path;

	@Resource
	private RedissonClient redissonClient;

	@Resource
	private MgCopyrightLvideoDownloadInfoMapper mgCopyrightLvideoDownloadInfoMapper;


	public void extractFromDb() {

		log.info("waiting for queue loading");
		final AutoLoadingQueue<MgCopyrightLvideoDownloadInfo> queue = getQueue();
		log.info("extract worker is starting");
		final ExecutorService executorService = Executors.newFixedThreadPool(concurrency);
		for (int i = 0; i < concurrency; i++) {
			executorService.submit(() -> {
				while (true) {
					final MgCopyrightLvideoDownloadInfo downloadInfo = queue.take();
					if (downloadInfo == null) {
						log.info("queue is empty, sleep 1min");
						TimeUnit.MINUTES.sleep(1);
						continue;
					}
					try {
						final String fileMd5 = downloadInfo.getFileMd5();
						final Path dnaPathDir = Paths.get(dnaRootPath, fileMd5.substring(0, 2), fileMd5.substring(2, 4));
						if (!Files.exists(dnaPathDir)) {
							Files.createDirectories(dnaPathDir);
						}
						final Path dnaPath = dnaPathDir.resolve(fileMd5 + ".dna");
						String errorInfo = execH265(downloadInfo.getFilePath(), dnaPath.toString());
						if (StringUtils.isBlank(errorInfo)) {

							final long size = Files.size(dnaPath);
							if (size < 200) {
								errorInfo = size + " is less than 200";
								mgCopyrightLvideoDownloadInfoMapper.updateErrorStatus(downloadInfo.getId(), "1", StringUtils.substring(errorInfo, 0, 1000));
							} else {
								mgCopyrightLvideoDownloadInfoMapper.updateStatusAndDnaPath(downloadInfo.getId(), "2", size, dnaPath.toString());
							}
						} else {
							mgCopyrightLvideoDownloadInfoMapper.updateErrorStatus(downloadInfo.getId(), "1", StringUtils.substring(errorInfo, 0, 1000));
						}
					} catch (Exception e) {
						log.error("{} extract unknown error", downloadInfo.getId(), e);
						mgCopyrightLvideoDownloadInfoMapper.updateErrorStatus(downloadInfo.getId(), "1", StringUtils.substring(("unknown error" + e.getMessage()), 0, 1000));
					}
				}
			});
		}
		executorService.shutdown();
		while (!executorService.isTerminated()) {
			try {
				TimeUnit.SECONDS.sleep(30);
			} catch (InterruptedException e) {
				Thread.currentThread().interrupt();
			}
		}
		queue.shutdown();
	}

	private AutoLoadingQueue<MgCopyrightLvideoDownloadInfo> getQueue() {

		return new AutoLoadingQueue<>(3 * concurrency, count -> {

			final RLock lock = redissonClient.getLock("update-queue");
			lock.lock();
			List<MgCopyrightLvideoDownloadInfo> downloadInfoList;
			try {
				final int limit = concurrency * 2;
				downloadInfoList = mgCopyrightLvideoDownloadInfoMapper.listHighNotExtracted(limit);
				if (downloadInfoList.isEmpty()) {
					log.info("high is empty, query from bs");
					downloadInfoList = mgCopyrightLvideoDownloadInfoMapper.listBsNotExtracted(limit);
				}

				if (downloadInfoList.isEmpty()) {
					log.info("bs is empty, query from ls");
					downloadInfoList = mgCopyrightLvideoDownloadInfoMapper.listLsNotExtracted(limit);
				}

				if (downloadInfoList.isEmpty()) {
					log.info("ls is empty, query from wang");
					downloadInfoList = mgCopyrightLvideoDownloadInfoMapper.listWangNotExtracted(limit);
				}

				if (downloadInfoList.isEmpty()) {
					log.info("load data is empty");
				} else {
					final List<Long> ids = downloadInfoList.stream().map(MgCopyrightLvideoDownloadInfo::getId).collect(Collectors.toList());
					mgCopyrightLvideoDownloadInfoMapper.updateExtracting(ids);
					log.info("queue add {}", ids.size());
				}
			} finally {
				lock.unlock();
			}
			return downloadInfoList;
		});
	}

	private String extract(String path, String dnaPath) {

		List<String> list = new ArrayList<>();
		list.add(extractImgPath);
		list.add("-i");
		list.add("\"" + path + "\"");
		list.add("-ov");
		list.add("\"" + dnaPath + "\"");

		String errorInfo = null;
		ProcessBuilder builder = new ProcessBuilder(list);
		builder.redirectErrorStream(true);
		try {
			Process process = builder.start();
			BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(process.getInputStream()));
			String line;
			while ((line = bufferedReader.readLine()) != null) {
				if (line.contains("xception")) {
					if (line.contains("CvCreateFileCapture")) {
						errorInfo = execH265(path, dnaPath);
					} else {
						errorInfo = line;
						log.error("extract {} error: {}", path, line);
					}
				}
			}
			process.waitFor();
			process.exitValue();
			process.destroyForcibly();
		} catch (Exception e) {
			errorInfo = "unknown error: " + e.getMessage();
			log.error(errorInfo, e);
		}
		return errorInfo;
	}


	private String execH265(String path, String dnaPath) {

		List<String> list = new ArrayList<>();
		list.add(extractImgH265Path);
		list.add("--lver");
		list.add("2");
		list.add("-i");
		list.add("\"" + path + "\"");
		list.add("-ov");
		list.add("\"" + dnaPath + "\"");

		String errorInfo = null;
		ProcessBuilder builder = new ProcessBuilder(list);
		builder.redirectErrorStream(true);
		try {
			Process process = builder.start();
			BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(process.getInputStream()));
			String line;
			while ((line = bufferedReader.readLine()) != null) {
				if (line.contains("xception")) {
					errorInfo = line;
				}
			}
			process.waitFor();
			process.exitValue();
			process.destroyForcibly();
		} catch (Exception e) {
			errorInfo = "unknown error: " + e.getMessage();
			log.error(errorInfo, e);
		}
		return errorInfo;
	}

	@PostConstruct
	public void checkParams() {
		Assert.isTrue(Files.isWritable(Paths.get(dnaRootPath)), dnaRootPath + " is not writable");
		Assert.isTrue(Files.exists(Paths.get(extractImgPath)), extractImgPath + "is not exist");
		Assert.isTrue(Files.exists(Paths.get(extractImgH265Path)), extractImgH265Path + " is not exist");
	}
}
