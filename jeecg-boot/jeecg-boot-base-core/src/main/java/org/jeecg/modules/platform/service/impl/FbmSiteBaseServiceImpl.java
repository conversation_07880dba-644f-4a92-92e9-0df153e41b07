package org.jeecg.modules.platform.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.util.JwtUtil;
import org.jeecg.entity.param.FbmSiteBaseAddParam;
import org.jeecg.entity.param.FbmSiteBaseEditParam;
import org.jeecg.entity.param.FbmSiteBaseExcelParam;
import org.jeecg.entity.param.FbmSiteBasePageParam;
import org.jeecg.entity.po.site.FbmSiteBase;
import org.jeecg.entity.po.site.FbmSiteContact;
import org.jeecg.entity.po.site.FbmSiteIcp;
import org.jeecg.entity.vo.FbmSitePageVo;
import org.jeecg.modules.platform.mapper.FbmSiteBaseMapper;
import org.jeecg.modules.platform.service.IFbmSiteBaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class FbmSiteBaseServiceImpl extends ServiceImpl<FbmSiteBaseMapper, FbmSiteBase> implements IFbmSiteBaseService {
    @Autowired
    FbmSiteIcpServiceImpl fbmSiteIcpService;
    @Autowired
    FbmSiteContactServiceImpl fbmSiteContactService;
    @Autowired
    private FbmSiteBaseMapper fbmSiteBaseMapper;

    @Override
    public List<Long> querySiteBaseIdBySiteShowName(String siteShowName) {

        final LambdaQueryWrapper<FbmSiteBase> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FbmSiteBase::getSiteShowName, siteShowName);
        queryWrapper.select(Collections.singletonList(FbmSiteBase::getId));
        final List<FbmSiteBase> list = this.list(queryWrapper);
        return list.stream().map(FbmSiteBase::getId).collect(Collectors.toList());
    }

    @Override
    public IPage<FbmSitePageVo> pageSiteAndContact(Page<FbmSiteBasePageParam> page,
                                                   FbmSiteBasePageParam fbmSiteBasePageParam) {
        return fbmSiteBaseMapper.pageSiteAndContact(page, fbmSiteBasePageParam);

    }

    @Override
    public List<FbmSiteBaseExcelParam> listSiteExcel(FbmSiteBasePageParam fbmSiteBasePageParam) {
        return fbmSiteBaseMapper.listSiteExcel(fbmSiteBasePageParam);
    }

    @Transactional
    @Override
    public String saveFbmSiteIcpContact(FbmSiteBaseAddParam fbmSiteBaseAddParam) {
        LambdaQueryWrapper<FbmSiteBase> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FbmSiteBase::getHost, fbmSiteBaseAddParam.getHost()).or().eq(FbmSiteBase::getSiteBaseName,
            fbmSiteBaseAddParam.getSiteShowName());  // 假设 host 是你要插入的值
        Long count = fbmSiteBaseMapper.selectCount(queryWrapper);

        if (count > 0) {
            // 直接返回友好提示，避免走到 SQL 报错
            return "siteBaseName或者host已存在";
        }

        FbmSiteBase fbmSiteBase = new FbmSiteBase();
        fbmSiteBase.setSiteShowName(fbmSiteBaseAddParam.getSiteShowName());
        fbmSiteBase.setDomain(fbmSiteBaseAddParam.getDomain());
        fbmSiteBase.setHost(fbmSiteBaseAddParam.getHost());
        fbmSiteBase.setSiteType(fbmSiteBaseAddParam.getSiteType());
        fbmSiteBase.setContentFirstCategory(fbmSiteBaseAddParam.getContentFirstCategory());
        fbmSiteBase.setContentSecondCategory(fbmSiteBaseAddParam.getContentSecondCategory());
        fbmSiteBase.setCreateUserId(
                JwtUtil.getLoginUser() != null ? Long.parseLong(JwtUtil.getLoginUser().getId()) : 0L);
        this.save(fbmSiteBase);
        Long baseId = fbmSiteBase.getId();
        FbmSiteIcp fbmSiteIcp = new FbmSiteIcp();
        fbmSiteIcp.setDomain(fbmSiteBaseAddParam.getDomain());
        fbmSiteIcp.setIcpOrgName(fbmSiteBaseAddParam.getIcpOrgName());
        fbmSiteIcp.setIcpNum(fbmSiteBaseAddParam.getIcpNum());
        fbmSiteIcp.setIcpOrgNature(fbmSiteBaseAddParam.getIcpOrgNature());
        fbmSiteIcp.setDomainServer(fbmSiteBaseAddParam.getDomainServer());
        fbmSiteIcp.setCreateUserId(
                JwtUtil.getLoginUser() != null ? Long.parseLong(JwtUtil.getLoginUser().getId()) : 0L);
        fbmSiteIcp.setBaseId(baseId);
        fbmSiteIcp.setHost(fbmSiteBaseAddParam.getHost());
        fbmSiteIcpService.save(fbmSiteIcp);
        FbmSiteContact fbmSiteContact = new FbmSiteContact();
        fbmSiteContact.setContactName(fbmSiteBaseAddParam.getContactName());
        fbmSiteContact.setContactEmail(fbmSiteBaseAddParam.getContactEmail());
        fbmSiteContact.setContactMobile(fbmSiteBaseAddParam.getContactMobile());
        fbmSiteContact.setSiteBaseId(baseId);
        fbmSiteContactService.save(fbmSiteContact);
        return "添加成功";
    }

    @Transactional
    @Override
    public void updateSiteIcpContact(FbmSiteBaseEditParam fbmSiteBaseEditParam) {
        Long baseId = fbmSiteBaseEditParam.getId();
        FbmSiteBase fbmSiteBase = new FbmSiteBase();
        fbmSiteBase.setSiteShowName(fbmSiteBaseEditParam.getSiteShowName());
        fbmSiteBase.setDomain(fbmSiteBaseEditParam.getDomain());
        fbmSiteBase.setHost(fbmSiteBaseEditParam.getHost());
        fbmSiteBase.setSiteType(fbmSiteBaseEditParam.getSiteType());
        fbmSiteBase.setContentFirstCategory(fbmSiteBaseEditParam.getContentFirstCategory());
        fbmSiteBase.setContentSecondCategory(fbmSiteBaseEditParam.getContentSecondCategory());
        fbmSiteBase.setId(baseId);
        this.updateById(fbmSiteBase);
        FbmSiteIcp fbmSiteIcp = new FbmSiteIcp();
        fbmSiteIcp.setDomain(fbmSiteBaseEditParam.getDomain());
        fbmSiteIcp.setIcpOrgName(fbmSiteBaseEditParam.getIcpOrgName());
        fbmSiteIcp.setIcpNum(fbmSiteBaseEditParam.getIcpNum());
        fbmSiteIcp.setIcpOrgNature(fbmSiteBaseEditParam.getIcpOrgNature());
        fbmSiteIcp.setDomainServer(fbmSiteBaseEditParam.getDomainServer());
        fbmSiteIcp.setBaseId(baseId);
        LambdaQueryWrapper<FbmSiteIcp> fbmSiteIcpLambdaQueryWrapper = new LambdaQueryWrapper<>();
        fbmSiteIcpLambdaQueryWrapper.eq(FbmSiteIcp::getBaseId, baseId);
        fbmSiteIcpService.update(fbmSiteIcp,fbmSiteIcpLambdaQueryWrapper);
        FbmSiteContact fbmSiteContact = new FbmSiteContact();
        fbmSiteContact.setContactName(fbmSiteBaseEditParam.getContactName());
        fbmSiteContact.setContactEmail(fbmSiteBaseEditParam.getContactEmail());
        fbmSiteContact.setContactMobile(fbmSiteBaseEditParam.getContactMobile());
        fbmSiteContact.setSiteBaseId(baseId);
        fbmSiteContactService.update(fbmSiteContact, new LambdaQueryWrapper<FbmSiteContact>().eq(FbmSiteContact::getSiteBaseId, baseId));
    }

    public void removeSiteContactIcp(long id) {
        // 1. 禁用站点（is_enabled=0）
        this.updateById(new FbmSiteBase().setId(id).setIsEnabled(0));

        // 2. 更新联系人状态（status=0）
        fbmSiteContactService.update(
                new UpdateWrapper<FbmSiteContact>()
                        .eq("site_base_id", id)
                        .set("status", "0")
        );

        // 3. 删除ICP记录
        fbmSiteIcpService.remove(
                new LambdaQueryWrapper<FbmSiteIcp>()
                        .eq(FbmSiteIcp::getBaseId, id)
        );
    }
}
