package org.jeecg.modules.platform.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.entity.FbmTortDataIptv;

import java.util.ArrayList;
import java.util.Map;

/**
 * @Description: iptv侵权表
 * @Author: jeecg-boot
 * @Date:   2024-09-05
 * @Version: V1.0
 */
public interface IFbmTortDataIptvService extends IService<FbmTortDataIptv> {

    Map<String, Long> getCountNumValid(ArrayList<Long> longs);
}
