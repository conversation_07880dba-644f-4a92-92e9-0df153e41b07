package org.jeecg.modules.platform.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.jeecg.entity.FbmTortDataPic;
import org.jeecg.entity.bo.MiguWorksSiteMailOfflineBo;
import org.jeecg.entity.bo.MiguWorksSiteMailOnlineBo;
import org.jeecg.entity.bo.SiteTopBo;
import org.jeecg.entity.param.MiguWorksSiteOfflinePrParam;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Description: 图片侵权表
 * @Author: jeecg-boot
 * @Date:   2024-09-05
 * @Version: V1.0
 */
@DS(value = "db-239")
public interface FbmTortDataPicMapper extends BaseMapper<FbmTortDataPic> {

    @Select(" <script> " +
            "  SELECT b.site_show_name as siteShowName,count(v.id) as tortNum from fbm_tort_data_pic v " +
            "  inner join fbm_site_base b on b.id=v.site_base_id   " +
            "  where tort_is_del=1 and works_id in " +
            "   <foreach collection=\"worksIds\" index=\"index\" item=\"item\" open=\"(\" close=\")\" separator=\",\">\n" +
            "                #{item}\n" +
            "   </foreach>  group by b.site_show_name  " +
            " </script>")
    List<SiteTopBo> groupBySiteShowName(@Param(value="worksIds") List<Long> worksIds);


    @Select(" <script> " +
            "  SELECT b.site_show_name as siteShowName,count(v.id) as tortNum from fbm_tort_data_pic v " +
            "  inner join fbm_site_base b on b.id=v.site_base_id   " +
            "  where tort_is_del=1 and works_id in " +
            "   <foreach collection=\"param.worksIds\" index=\"index\" item=\"item\" open=\"(\" close=\")\" separator=\",\">\n" +
            "                #{item}\n" +
            "   </foreach>" +
            "   and b.site_show_name in " +
            "   <foreach collection=\"param.siteShowNames\" index=\"index\" item=\"item\" open=\"(\" close=\")\" separator=\",\">\n" +
            "                #{item}\n" +
            "   </foreach>" +
            " and tort_status  not in (0,2,3,4) and  v.tort_first_send_mail_time is not null " +
            " and v.tort_offline_date is not null  " +
            " and tort_offline_date &lt; date_add(tort_first_send_mail_time, interval #{param.hour}  HOUR)     " +
            "      group by b.site_show_name  " +
            " </script>")
    List<MiguWorksSiteMailOfflineBo> getSiteMailOfflineCount(@Param(value="param") MiguWorksSiteOfflinePrParam param);


    @Select(" <script> " +
            "  SELECT b.site_show_name as siteShowName,count(v.id) as tortNum from fbm_tort_data_pic v " +
            "  inner join fbm_site_base b on b.id=v.site_base_id   " +
            "  where tort_is_del=1 and works_id in " +
            "   <foreach collection=\"param.worksIds\" index=\"index\" item=\"item\" open=\"(\" close=\")\" separator=\",\">\n" +
            "                #{item}\n" +
            "   </foreach> and tort_status in (0,2,3,4) and  v.tort_first_send_mail_time is not null " +
            " and v.tort_last_send_mail_time is not null  " +
            "  and tort_last_send_mail_time &lt; date_add(tort_first_send_mail_time, interval #{param.hour} HOUR)     " +
            "      group by b.site_show_name  " +
            " </script>")
    List<MiguWorksSiteMailOnlineBo> getSiteMailOnlineCount(@Param(value="param") MiguWorksSiteOfflinePrParam param);


    @Select("<script>" +
            "SELECT COUNT(*) as tortCount, COUNT(DISTINCT site_base_id) as siteCount " +
            "FROM fbm_tort_data_pic " +
            "WHERE tort_is_del = 1 " +
            "<if test='worksIds != null and worksIds.size() > 0'>" +
            "   AND works_id IN " +
            "   <foreach collection='worksIds' item='id' open='(' separator=',' close=')'>" +
            "       #{id}" +
            "   </foreach>" +
            "</if>" +
            "</script>")
    Map<String, Long> getCountNum(@Param(value="worksIds") List<Long> worksIds);

    /**
     * 按月份统计侵权数
     */
    @Select("<script>" +
            "SELECT MONTH(data_create_time) as month, COUNT(*) as tortCount " +
            "FROM fbm_tort_data_pic " +
            "WHERE tort_is_del = 1 AND data_create_time IS NOT NULL " +
            "AND YEAR(data_create_time) = YEAR(CURDATE()) " +
            "<if test='worksIds != null and worksIds.size() > 0'>" +
            "   AND works_id IN " +
            "   <foreach collection='worksIds' item='id' open='(' separator=',' close=')'>" +
            "       #{id}" +
            "   </foreach>" +
            "</if>" +
            "GROUP BY MONTH(data_create_time) " +
            "ORDER BY month" +
            "</script>")
    List<Map<String, Object>> getMonthlyTortCount(@Param(value="worksIds") List<Long> worksIds);

    @Select("<script>" +
            "SELECT COUNT(*) as tortCount, COUNT(DISTINCT site_base_id) as siteCount " +
            "FROM fbm_tort_data_pic " +
            "WHERE tort_is_del != 1 " +
            "<if test='worksIds != null and worksIds.size() > 0'>" +
            "   AND works_id IN " +
            "   <foreach collection='worksIds' item='id' open='(' separator=',' close=')'>" +
            "       #{id}" +
            "   </foreach>" +
            "</if>" +
            "</script>")
    Map<String, Long> getCountNumInvalid(@Param(value="worksIds")ArrayList<Long> worksIds);
}
