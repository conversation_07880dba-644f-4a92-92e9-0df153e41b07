<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.platform.mapper.FbmSiteBaseMapper">

    <select id="pageSiteAndContact" resultType="org.jeecg.entity.vo.FbmSitePageVo">
        select s.id, s.site_show_name, s.domain, s.host, c.contact_name, s.site_type,c.contact_email, c.contact_mobile
        from
        fbm_site_base s
        left join fbm_site_contact c on s.id = c.site_base_id
        where
        c.status = 1 and s.is_enabled = 1
        <if test="param.siteShowName != null and param.siteShowName != ''">
            and s.site_show_name like concat('%', #{param.siteShowName}, '%')
        </if>
        <if test="param.domain != null and param.domain != ''">
            and s.domain like concat('%', #{param.domain}, '%')
        </if>
        <if test="param.host != null and param.host != ''">
            and s.host like concat('%', #{param.host}, '%')
        </if>
        <if test="param.contactName != null and param.contactName != ''">
            and c.contact_name like concat('%', #{param.contactName}, '%')
        </if>
        <if test="param.contactEmail != null and param.contactEmail != ''">
            and c.contact_email like concat('%', #{param.contactEmail}, '%')
        </if>
        <if test="param.contactMobile != null and param.contactMobile != ''">
            and c.contact_mobile like concat('%', #{param.contactMobile}, '%')
        </if>
    </select>
    <select id="listSiteExcel" resultType="org.jeecg.entity.param.FbmSiteBaseExcelParam">
        select s.id, s.site_show_name, s.domain, s.host,s.site_type, c.contact_name, c.contact_email,
        c.contact_mobile ,i.icp_num, i.icp_org_name, i.icp_org_nature, i.domain_server
        from fbm_site_base s
        left join fbm_site_contact c on s.id = c.site_base_id left join fbm_site_icp i on s.id = i.base_id
        where
        c.status = 1 and s.is_enabled = 1
        <if test="param.siteShowName != null and param.siteShowName != ''">
            and s.site_show_name like concat('%', #{param.siteShowName}, '%')
        </if>
        <if test="param.domain != null and param.domain != ''">
            and s.domain like concat('%', #{param.domain}, '%')
        </if>
        <if test="param.host != null and param.host != ''">
            and s.host like concat('%', #{param.host}, '%')
        </if>
        <if test="param.contactName != null and param.contactName != ''">
            and c.contact_name like concat('%', #{param.contactName}, '%')
        </if>
        <if test="param.contactEmail != null and param.contactEmail != ''">
            and c.contact_email like concat('%', #{param.contactEmail}, '%')
        </if>
        <if test="param.contactMobile != null and param.contactMobile != ''">
            and c.contact_mobile like concat('%', #{param.contactMobile}, '%')
        </if>
    </select>
</mapper>