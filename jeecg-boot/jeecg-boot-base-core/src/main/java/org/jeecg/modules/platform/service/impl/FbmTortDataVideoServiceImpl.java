package org.jeecg.modules.platform.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.entity.FbmTortDataVideo;
import org.jeecg.entity.bo.*;
import org.jeecg.entity.param.MiguWorksSiteOfflinePrParam;
import org.jeecg.entity.param.SelectVvParam;
import org.jeecg.entity.vo.account.AccountWorkDetailVo;
import org.jeecg.modules.platform.mapper.FbmTortDataVideoMapper;
import org.jeecg.modules.platform.service.IFbmTortDataVideoService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @Description: 视频侵权表
 * @Author: jeecg-boot
 * @Date: 2024-09-05
 * @Version: V1.0
 */
@Service
public class FbmTortDataVideoServiceImpl extends ServiceImpl<FbmTortDataVideoMapper, FbmTortDataVideo> implements IFbmTortDataVideoService {

	public long sumVv(SelectVvParam param) {

		return this.baseMapper.sumVv(param);
	}

	public List<SiteTopBo> groupBySiteShowName(List<Long> worksIds) {
		return this.baseMapper.groupBySiteShowName(worksIds);
	}

	public List<PublisherTopBo> groupByPublisher(List<Long> worksIds) {
		return this.baseMapper.groupByPublisher(worksIds);
	}

	public List<UrlVvBo> groupByUrl(List<Long> worksIds) {
		return this.baseMapper.groupByUrl(worksIds);
	}

	public List<MiguWorksSiteMailOfflineBo> getSiteMailOfflineCount(MiguWorksSiteOfflinePrParam param) {
		return this.baseMapper.getSiteMailOfflineCount(param);
	}

	public List<MiguWorksSiteMailOnlineBo> getSiteMailOnlineCount(MiguWorksSiteOfflinePrParam param) {
		return this.baseMapper.getSiteMailOnlineCount(param);
	}

	@Override
	public List<FbmTortDataVideo> listByPublisher(long lastId,
												  String siteShowName, String publisherId, String publisher) {
		return this.baseMapper.listByPublisher(lastId, siteShowName, publisherId, publisher);
	}

	@Override
	public AccountWorkDetailVo.TortClueInfo selectTortClueInfo(Long worksId) {
		return this.baseMapper.selectTortClueInfo(worksId);
	}
	@Override
	public Map<String, Long> getCountNum(List<Long> worksIds){
		return this.baseMapper.getCountNum(worksIds);
	}

	@Override
	public Map<String, Long> getCountNumValid(ArrayList<Long> longs) {
		return this.baseMapper.getCountNumInvalid(longs);
	}

	public List<Map<String, Object>> getMonthlyTortCount(List<Long> worksIds) {
		return this.baseMapper.getMonthlyTortCount(worksIds);
	}

}
