package org.jeecg.modules.platform.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.entity.param.FbmSiteBaseExcelParam;
import org.jeecg.entity.param.FbmSiteBasePageParam;
import org.jeecg.entity.vo.FbmSitePageVo;
import org.jeecg.entity.po.site.FbmSiteBase;

import java.util.List;

public interface FbmSiteBaseMapper extends BaseMapper<FbmSiteBase> {

    IPage<FbmSitePageVo> pageSiteAndContact(@Param("page") Page<FbmSiteBasePageParam> page,
                                            @Param("param") FbmSiteBasePageParam fbmSiteBasePageParam);

    List<FbmSiteBaseExcelParam> listSiteExcel(@Param("param") FbmSiteBasePageParam fbmSiteBasePageParam);
}
