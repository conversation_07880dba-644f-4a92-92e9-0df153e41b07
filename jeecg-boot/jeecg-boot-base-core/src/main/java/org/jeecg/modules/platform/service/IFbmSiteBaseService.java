package org.jeecg.modules.platform.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.entity.param.FbmSiteBaseAddParam;
import org.jeecg.entity.param.FbmSiteBaseEditParam;
import org.jeecg.entity.param.FbmSiteBaseExcelParam;
import org.jeecg.entity.param.FbmSiteBasePageParam;
import org.jeecg.entity.vo.FbmSitePageVo;
import org.jeecg.entity.po.site.FbmSiteBase;

import java.util.List;

public interface IFbmSiteBaseService extends IService<FbmSiteBase> {

	List<Long> querySiteBaseIdBySiteShowName(String platformName);

    IPage<FbmSitePageVo> pageSiteAndContact(Page<FbmSiteBasePageParam> page,
                                            FbmSiteBasePageParam fbmSiteBasePageParam);

    List<FbmSiteBaseExcelParam> listSiteExcel(FbmSiteBasePageParam fbmSiteBasePageParam);

    String saveFbmSiteIcpContact(FbmSiteBaseAddParam fbmSiteBaseAddParam);

    void updateSiteIcpContact(FbmSiteBaseEditParam fbmSiteBaseEditParam);

    void removeSiteContactIcp(long l);
}
