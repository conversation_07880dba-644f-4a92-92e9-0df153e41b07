package org.jeecg.modules.platform.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.entity.FbmTortDataAudio;

import java.util.ArrayList;
import java.util.Map;

/**
 * @Description: 视频侵权表
 * @Author: jeecg-boot
 * @Date:   2024-09-05
 * @Version: V1.0
 */
public interface IFbmTortDataAudioService extends IService<FbmTortDataAudio> {

    Map<String, Long> getCountNumValid(ArrayList<Long> longs);
}
