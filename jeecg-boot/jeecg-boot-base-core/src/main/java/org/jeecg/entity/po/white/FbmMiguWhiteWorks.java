package org.jeecg.entity.po.white;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * @Description: 白名单和作品关联表
 * @Author: jeecg-boot
 * @Date:   2025-09-01
 * @Version: V1.0
 */
@Data
@TableName("fbm_migu_white_works")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="fbm_migu_white_works对象", description="白名单和作品关联表")
public class FbmMiguWhiteWorks {
	@TableId(type = IdType.AUTO )
    @ApiModelProperty(value = "主键")
	private java.lang.Long id;
    
	/**miguJobId*/
	@Excel(name = "miguJobId", width = 15)
    @ApiModelProperty(value = "miguWhiteId")
	private java.lang.Long miguWhiteId;
	/**作品id*/
	@Excel(name = "作品id", width = 15)
    @ApiModelProperty(value = "作品id")
	private java.lang.String worksId;
}
