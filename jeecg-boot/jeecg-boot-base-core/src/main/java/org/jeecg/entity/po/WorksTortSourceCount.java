package org.jeecg.entity.po;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * @Description: 作品侵权来源数量统计
 * @Author: jeecg-boot
 * @Date: 2025-09-05
 * @Version: V1.0
 */
@Data
@TableName("works_tort_source_count")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "works_tort_source_count对象", description = "作品侵权来源数量统计")
public class WorksTortSourceCount {

    /**
     * worksId
     */
    @Excel(name = "worksId", width = 15)
    @ApiModelProperty(value = "worksId")
    private java.lang.Integer worksId;
    /**
     * 线索来源
     */
    @Excel(name = "线索来源", width = 15)
    @ApiModelProperty(value = "线索来源")
    private java.lang.String tortSource;
    /**
     * 线索数量
     */
    @Excel(name = "线索数量", width = 15)
    @ApiModelProperty(value = "线索数量")
    private java.lang.String tortCount;
    /**
     * 占比
     */
    @Excel(name = "占比", width = 15)
    @ApiModelProperty(value = "占比")
    private java.lang.String rate;
}
