package org.jeecg.entity.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: ggr
 * @CreateTime: 2025-09-02
 * @Description:
 * @Version: 1.0
 */

@Data
public class FbmSiteBasePageParam {
    @ApiModelProperty(value = "平台名称")
    String siteShowName;
    @ApiModelProperty(value = "一级域名")
    String domain;
    @ApiModelProperty(value = "二级域名")
    String host;



    @ApiModelProperty(value = "联系人")
    String contactName;
    @ApiModelProperty(value = "联系人邮箱")
    String contactEmail;
    @ApiModelProperty(value = "联系人手机")
    String contactMobile;
}
