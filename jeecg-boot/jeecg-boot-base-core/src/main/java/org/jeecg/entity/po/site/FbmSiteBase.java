package org.jeecg.entity.po.site;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@TableName("fbm_site_base")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "fbm_site_base对象", description = "1")
public class FbmSiteBase {

	/**
	 * id
	 */
	@TableId(type = IdType.AUTO)
	@ApiModelProperty(value = "id")
	private Long id;
	/**
	 * 侵权公司产品id
	 */
	@Excel(name = "侵权公司产品id", width = 15)
	@ApiModelProperty(value = "侵权公司产品id")
	private Long companyProductId;
	/**
	 * 站点名，小网站为host
	 */
	@Excel(name = "站点名，小网站为host", width = 15)
	@ApiModelProperty(value = "站点名，小网站为host")
	private String siteBaseName;
	/**
	 * 业务导出时站点名,小网站即为小网站
	 */
	@Excel(name = "业务导出时站点名,小网站即为小网站", width = 15)
	@ApiModelProperty(value = "业务导出时站点名,小网站即为小网站")
	private String siteShowName;
	/**
	 * show name en
	 */
	@Excel(name = "show name en", width = 15)
	@ApiModelProperty(value = "show name en")
	private String siteShowNameEn;
	/**
	 * host, 默认为www.xxx.com
	 */
	@Excel(name = "host, 默认为www.xxx.com", width = 15)
	@ApiModelProperty(value = "host, 默认为www.xxx.com")
	private String host;
	/**
	 * domain
	 */
	@Excel(name = "domain", width = 15)
	@ApiModelProperty(value = "domain")
	private String domain;
	/**
	 * 图标URL
	 */
	@Excel(name = "图标URL", width = 15)
	@ApiModelProperty(value = "图标URL")
	private String iconUrl;
	/**
	 * 内容一级类型,1:视频;2:音频;3:文字;4:图片
	 */
	@Excel(name = "内容一级类型,1:视频;2:音频;3:文字;4:图片", width = 15)
	@ApiModelProperty(value = "内容一级类型,1:视频;2:音频;3:文字;4:图片")
	private String contentFirstCategory;
	/**
	 * 内容二级类型,101:视频;109:直播
	 */
	@Excel(name = "内容二级类型,101:视频;109:直播", width = 15)
	@ApiModelProperty(value = "内容二级类型,101:视频;109:直播")
	private String contentSecondCategory;
	/**
	 * PR(百度)
	 */
	@Excel(name = "PR(百度)", width = 15)
	@ApiModelProperty(value = "PR(百度)")
	private Integer prBaidu;
	/**
	 * PR(google)
	 */
	@Excel(name = "PR(google)", width = 15)
	@ApiModelProperty(value = "PR(google)")
	private Integer prGoogle;
	/**
	 * PR(搜狗)
	 */
	@Excel(name = "PR(搜狗)", width = 15)
	@ApiModelProperty(value = "PR(搜狗)")
	private Integer prSogou;
	/**
	 * PR(360)
	 */
	@Excel(name = "PR(360)", width = 15)
	@ApiModelProperty(value = "PR(360)")
	private Integer pr_360;
	/**
	 * alexa排名
	 */
	@Excel(name = "alexa排名", width = 15)
	@ApiModelProperty(value = "alexa排名")
	private Integer alexa;
	/**
	 * 是否有效,0:无效;1:有效
	 */
	@Excel(name = "是否有效,0:无效;1:有效", width = 15)
	@ApiModelProperty(value = "是否有效,0:无效;1:有效")
	private int isEnabled;
	/**
	 * 创建者用户id
	 */
	@Excel(name = "创建者用户id", width = 15)
	@ApiModelProperty(value = "创建者用户id")
	private Long createUserId;
	/**
	 * 创建时间
	 */
	@Excel(name = "创建时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "创建时间")
	private Date createTime;
	/**
	 * 最后一次更新时间
	 */
	@Excel(name = "最后一次更新时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "最后一次更新时间")
	private Date modifyTime;
	/**
	 * 站点下线总数
	 */
	@Excel(name = "站点下线总数", width = 15)
	@ApiModelProperty(value = "站点下线总数")
	private Integer tortCountOff;
	/**
	 * 站点侵权总数
	 */
	@Excel(name = "站点侵权总数", width = 15)
	@ApiModelProperty(value = "站点侵权总数")
	private Integer tortCount;
	/**
	 * 站点最近一次侵权时间
	 */
	@Excel(name = "站点最近一次侵权时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "站点最近一次侵权时间")
	private Date lastTortTime;
	/**
	 * 1:主流网站，2:小网站
	 */
	@Excel(name = "1:主流网站，2:小网站", width = 15)
	@ApiModelProperty(value = "1:主流网站，2:小网站")
	private String siteType;
	/**
	 * url
	 */
	@Excel(name = "url", width = 15)
	@ApiModelProperty(value = "url")
	private String url;
	/**
	 * 0.手动创建 1,统一入库创建
	 */
	@Excel(name = "0.手动创建 1,统一入库创建", width = 15)
	@ApiModelProperty(value = "0.手动创建 1,统一入库创建")
	private Integer source;
}
