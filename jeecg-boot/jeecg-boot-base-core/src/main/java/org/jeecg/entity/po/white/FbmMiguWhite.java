package org.jeecg.entity.po.white;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * @Description: 白名单管理
 * @Author: jeecg-boot
 * @Date:   2025-08-29
 * @Version: V1.0
 */
@Data
@TableName("fbm_migu_white")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="fbm_migu_white对象", description="白名单管理")
public class FbmMiguWhite {
    
	/**主键*/
	@TableId(type = IdType.AUTO )
    @ApiModelProperty(value = "主键")
	private java.lang.Long id;
	/**站点名称*/
	@Excel(name = "站点名称", width = 15)
    @ApiModelProperty(value = "站点名称")
	private java.lang.String siteBaseShowName;
	/**站点id*/
	@Excel(name = "站点id", width = 15)
    @ApiModelProperty(value = "站点id")
	private java.lang.String siteBaseId;
	/**发布者名称*/
	@Excel(name = "发布者名称", width = 15)
    @ApiModelProperty(value = "发布者名称")
	private java.lang.String publisher;
	/**发布者id*/
	@Excel(name = "发布者id", width = 15)
    @ApiModelProperty(value = "发布者id")
	private java.lang.String publisherId;
	/**主页链接*/
	@Excel(name = "主页链接", width = 15)
    @ApiModelProperty(value = "主页链接")
	private java.lang.String homepageLink;
	/**主页id*/
	@Excel(name = "主页id", width = 15)
    @ApiModelProperty(value = "主页id")
	private java.lang.String homepageId;
	/**类型*/
	@Excel(name = "类型", width = 15)
    @ApiModelProperty(value = "类型")
	private java.lang.Integer type;
	@ApiModelProperty(value = "白名单来源")
	@Excel(name = "白名单来源", width = 15)
	private String whiteSource;
}
