package org.jeecg.entity.param;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.util.List;

/**
 * @Author: ggr
 * @CreateTime: 2025-09-03
 * @Description:
 * @Version: 1.0
 */

@Data
public class FbmMiguWhiteEditParam {

        /**主键*/
        @TableId(type = IdType.AUTO )
        @ApiModelProperty(value = "主键")
        private java.lang.Long id;
        /**站点名称*/
        @Excel(name = "站点名称", width = 15)
        @ApiModelProperty(value = "站点名称")
        private java.lang.String siteBaseShowName;
        /**站点id*/
        @Excel(name = "站点id", width = 15)
        @ApiModelProperty(value = "站点id")
        private java.lang.String siteBaseId;
        /**发布者名称*/
        @Excel(name = "发布者名称", width = 15)
        @ApiModelProperty(value = "发布者名称")
        private java.lang.String publisher;
        /**发布者id*/
        @Excel(name = "发布者id", width = 15)
        @ApiModelProperty(value = "发布者id")
        private java.lang.String publisherId;
        /**主页链接*/
        @Excel(name = "主页链接", width = 15)
        @ApiModelProperty(value = "主页链接")
        private java.lang.String homepageLink;
        /**主页id*/
        @Excel(name = "主页id", width = 15)
        @ApiModelProperty(value = "主页id")
        private java.lang.String homepageId;
        /**类型*/
        @Excel(name = "类型", width = 15)
        @ApiModelProperty(value = "类型")
        private java.lang.Integer type;

        @ApiModelProperty(value = "作品id集合")
        private List<Long> worksIdList;

        @ApiModelProperty(value = "白名单来源")
        @Excel(name = "白名单来源", width = 15)
        private String whiteSource;

}
