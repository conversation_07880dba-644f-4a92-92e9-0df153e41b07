package org.jeecg.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author: ggr
 * @CreateTime: 2025-09-04
 * @Description:
 * @Version: 1.0
 */

@Data
@Slf4j
public class FbmWorksPageVo {
    @ApiModelProperty("作品id")
    private Long id;
    @ApiModelProperty("作品名称")
    private String worksName;
    @ApiModelProperty("侵权数")
    private Long tortCount;
    @ApiModelProperty("演员")
    private String actors;
    @ApiModelProperty("导演")
    private String director;
    @ApiModelProperty("一级分类")
    private String firstCategory;
    @ApiModelProperty("二级分类")
    private String secondCategory;

}
