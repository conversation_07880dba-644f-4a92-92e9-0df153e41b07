package org.jeecg.entity.po.site;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Description: 站点icp备案表
 * @Author: jeecg-boot
 * @Date: 2025-09-01
 * @Version: V1.0
 */
@Data
@TableName("fbm_site_icp")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "fbm_site_icp对象", description = "站点icp备案表")
public class FbmSiteIcp {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "id")
    private Integer id;
    /**
     * domain
     */
    @Excel(name = "domain", width = 15)
    @ApiModelProperty(value = "domain")
    private String domain;
    /**
     * ICP备案号
     */
    @Excel(name = "ICP备案号", width = 15)
    @ApiModelProperty(value = "ICP备案号")
    private String icpNum;
    /**
     * ICP备案单位名称
     */
    @Excel(name = "ICP备案单位名称", width = 15)
    @ApiModelProperty(value = "ICP备案单位名称")
    private String icpOrgName;
    /**
     * ICP备案单位性质
     */
    @Excel(name = "ICP备案单位性质", width = 15)
    @ApiModelProperty(value = "ICP备案单位性质")
    private String icpOrgNature;
    /**
     * 创建者用户id
     */
    @Excel(name = "创建者用户id", width = 15)
    @ApiModelProperty(value = "创建者用户id")
    private Long createUserId;
    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 最后一次更新时间
     */
    @Excel(name = "最后一次更新时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最后一次更新时间")
    private Date modifyTime;
    /**
     * fbm_site_base表id
     */
    @Excel(name = "fbm_site_base表id", width = 15)
    @ApiModelProperty(value = "fbm_site_base表id")
    private Long baseId;
    /**
     * host, 默认为www.xxx.com
     */
    @Excel(name = "host, 默认为www.xxx.com", width = 15)
    @ApiModelProperty(value = "host, 默认为www.xxx.com")
    private String host;
    /**
     * 备案时间
     */
    @Excel(name = "备案时间", width = 15)
    @ApiModelProperty(value = "备案时间")
    private String icpTime;
    /**
     * 备案站点名称
     */
    @Excel(name = "备案站点名称", width = 15)
    @ApiModelProperty(value = "备案站点名称")
    private String icpName;
    /**
     * 域名服务器
     */
    @Excel(name = "域名服务器", width = 15)
    @ApiModelProperty(value = "域名服务器")
    private String domainServer;
    /**
     * 备案信息所在地
     */
    @Excel(name = "备案信息所在地", width = 15)
    @ApiModelProperty(value = "备案信息所在地")
    private String address;
    /**
     * 域名注册商
     */
    @Excel(name = "域名注册商", width = 15)
    @ApiModelProperty(value = "域名注册商")
    private String domainRegistrar;
    /**
     * dns服务
     */
    @Excel(name = "dns服务", width = 15)
    @ApiModelProperty(value = "dns服务")
    private String dnsServer;
    /**
     * 地址编码
     */
    @Excel(name = "地址编码", width = 15)
    @ApiModelProperty(value = "地址编码")
    private String addressCode;
    /**
     * 0未校验，1人工校验
     */
    @Excel(name = "0未校验，1人工校验", width = 15)
    @ApiModelProperty(value = "0未校验，1人工校验")
    private Integer checkStatus;
    /**
     * 0,无证，1有证
     */
    @Excel(name = "0,无证，1有证", width = 15)
    @ApiModelProperty(value = "0,无证，1有证")
    private Integer isCredentials;
    /**
     * 排序
     */
    @Excel(name = "排序", width = 15)
    @ApiModelProperty(value = "排序")
    private Integer sort;
}
