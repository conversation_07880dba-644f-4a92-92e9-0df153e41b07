package org.jeecg.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * @Description: 作品表
 * @Author: jeecg-boot
 * @Date:   2024-09-05
 * @Version: V1.0
 */
@Data
@TableName("fbm_works")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="fbm_works对象", description="作品表")
public class FbmWorks {
    
	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
	private Long id;
	/**version*/
	@Excel(name = "version", width = 15)
    @ApiModelProperty(value = "version")
	private Integer version;
	/**创建时间,默认为当前*/
	@Excel(name = "创建时间,默认为当前", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间,默认为当前")
	private Date createTime;
	/**创建者用户全名*/
	@Excel(name = "创建者用户全名", width = 15)
    @ApiModelProperty(value = "创建者用户全名")
	private String createUserFullName;
	/**创建者用户id*/
	@Excel(name = "创建者用户id", width = 15)
    @ApiModelProperty(value = "创建者用户id")
	private Long createUserId;
	/**该作品涉及到的关键词,多个*/
	@Excel(name = "该作品涉及到的关键词,多个", width = 15)
    @ApiModelProperty(value = "该作品涉及到的关键词,多个")
	private String keywords;
	/**修改时间,默认为当前*/
	@Excel(name = "修改时间,默认为当前", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间,默认为当前")
	private Date modifyTime;
	/**作品名*/
	@Excel(name = "作品名", width = 15)
    @ApiModelProperty(value = "作品名")
	private String worksName;
	/**一级类型,1:视频类;2:音频类;3:文字类;4:图片类;*/
	@Excel(name = "一级类型,1:视频类;2:音频类;3:文字类;4:图片类;", width = 15)
    @ApiModelProperty(value = "一级类型,1:视频类;2:音频类;3:文字类;4:图片类;")
	private String firstCategory;
	/**二级类型,11:电影;12:电视剧;13:自制剧;14:动漫;15:综艺;16:体育;17:其他|
	21:音乐;22:有声阅读;23:其他|
	31:新闻;32:独家报道;33:文学;34:自媒体;35:其他|
	41.产品图片;42:商标LOGO;43:摄影图片;44:其他*/
	@Excel(name = "二级类型,11:电影;12:电视剧;13:自制剧;14:动漫;15:综艺;16:体育;17:其他")
	private java.lang.String secondCategory;
	/**author*/
	@Excel(name = "author", width = 15)
    @ApiModelProperty(value = "author")
	private java.lang.String author;
	/**extInfo*/
	@Excel(name = "extInfo", width = 15)
    @ApiModelProperty(value = "extInfo")
	private java.lang.String extInfo;
	/**该作品涉及到的站内搜索关键词,多个*/
	@Excel(name = "该作品涉及到的站内搜索关键词,多个", width = 15)
    @ApiModelProperty(value = "该作品涉及到的站内搜索关键词,多个")
	private String searchKeywords;
	/**该作品涉及到的站内屏蔽关键词,多个*/
	@Excel(name = "该作品涉及到的站内屏蔽关键词,多个", width = 15)
    @ApiModelProperty(value = "该作品涉及到的站内屏蔽关键词,多个")
	private String filterKeywords;
	/**该作品涉及到的站内包含的关键词,多个*/
	@Excel(name = "该作品涉及到的站内包含的关键词,多个", width = 15)
    @ApiModelProperty(value = "该作品涉及到的站内包含的关键词,多个")
	private String includeKeywords;
	/**匹配关键词，用来标题匹配使用*/
	@Excel(name = "匹配关键词，用来标题匹配使用", width = 15)
    @ApiModelProperty(value = "匹配关键词，用来标题匹配使用")
	private String matchKeywords;
	/**该作品详细信息*/
	@Excel(name = "该作品详细信息", width = 15)
    @ApiModelProperty(value = "该作品详细信息")
	private String worksDetail;
	/**视频作品级别，1-专项，2-AA级，3-A级，4-B级*/
	@Excel(name = "视频作品级别，1-专项，2-AA级，3-A级，4-B级", width = 15)
    @ApiModelProperty(value = "视频作品级别，1-专项，2-AA级，3-A级，4-B级")
	private java.lang.String worksType;
	/**0：常规 不维权 1：需要维权*/
	@Excel(name = "0：常规 不维权 1：需要维权", width = 15)
    @ApiModelProperty(value = "0：常规 不维权 1：需要维权")
	private java.lang.Integer rightsStatus;
	/**维权起始时间*/
	@Excel(name = "维权起始时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "维权起始时间")
	private Date rightsStartTime;
	/**维权结束时间*/
	@Excel(name = "维权结束时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "维权结束时间")
	private Date rightsEndTime;
	/**1：重点监测 0：常规监测（目前仅划分0,1 后续跟进业务需要会扩展 S：4、AAA：3、AA：2、A：1）*/
	@Excel(name = "1：重点监测 0：常规监测（目前仅划分0,1 后续跟进业务需要会扩展 S：4、AAA：3、AA：2、A：1）", width = 15)
    @ApiModelProperty(value = "1：重点监测 0：常规监测（目前仅划分0,1 后续跟进业务需要会扩展 S：4、AAA：3、AA：2、A：1）")
	private Integer priority;
	/**业务端生成的唯一code，传送到版权库，然后通过此code，从版权库获取该作品的唯一标示*/
	@Excel(name = "业务端生成的唯一code，传送到版权库，然后通过此code，从版权库获取该作品的唯一标示", width = 15)
    @ApiModelProperty(value = "业务端生成的唯一code，传送到版权库，然后通过此code，从版权库获取该作品的唯一标示")
	private String md5Code;
	/**版权库审核通过后，回调业务端，业务端获取到该作品的唯一标示*/
	@Excel(name = "版权库审核通过后，回调业务端，业务端获取到该作品的唯一标示", width = 15)
    @ApiModelProperty(value = "版权库审核通过后，回调业务端，业务端获取到该作品的唯一标示")
	private String assetCode;
	/**该作品搜索屏蔽关键词,多个*/
	@Excel(name = "该作品搜索屏蔽关键词,多个", width = 15)
    @ApiModelProperty(value = "该作品搜索屏蔽关键词,多个")
	private String crawlDeniedKeywords;
	/**该作品的相关信息如导演，主演等*/
	@Excel(name = "该作品的相关信息如导演，主演等", width = 15)
    @ApiModelProperty(value = "该作品的相关信息如导演，主演等")
	private String worksInfo;
	/**过滤时间*/
	@Excel(name = "过滤时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "过滤时间")
	private Date filterTime;
	/**图片路径*/
	@Excel(name = "图片路径", width = 15)
    @ApiModelProperty(value = "图片路径")
	private String imagePath;
	/**dataUniqueKey*/
	@Excel(name = "dataUniqueKey", width = 15)
    @ApiModelProperty(value = "dataUniqueKey")
	private String dataUniqueKey;
	/**isShow*/
	@Excel(name = "isShow", width = 15)
    @ApiModelProperty(value = "isShow")
	private Integer isShow;
	/**authorId*/
	@Excel(name = "authorId", width = 15)
    @ApiModelProperty(value = "authorId")
	private String authorId;
	/**appId*/
	@Excel(name = "appId", width = 15)
    @ApiModelProperty(value = "appId")
	private String appId;
	/**authorUrl*/
	@Excel(name = "authorUrl", width = 15)
    @ApiModelProperty(value = "authorUrl")
	private String authorUrl;
	/**realWorksName*/
	@Excel(name = "realWorksName", width = 15)
    @ApiModelProperty(value = "realWorksName")
	private String realWorksName;
	/**registerPlatformCode*/
	@Excel(name = "registerPlatformCode", width = 15)
    @ApiModelProperty(value = "registerPlatformCode")
	private Integer registerPlatformCode;
	@ApiModelProperty(value = "keywordJson")
	private String keywordJson;
	@ApiModelProperty(value = "exclusive_rights")
	private String exclusiveRights;
	@ApiModelProperty(value = "contact_person")
	private String contactPerson;
	@ApiModelProperty(value = "content_rating")
	private String contentRating;
	@ApiModelProperty(value = "broadcast_platform")
	private String broadcastPlatform;
	@ApiModelProperty(value = "重点关注平台")
	private String site;


}
