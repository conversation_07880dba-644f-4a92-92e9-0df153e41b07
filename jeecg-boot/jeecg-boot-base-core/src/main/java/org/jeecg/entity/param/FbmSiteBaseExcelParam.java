package org.jeecg.entity.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * @Author: ggr
 * @CreateTime: 2025-09-01
 * @Description:
 * @Version: 1.0
 */

@Data
@Slf4j
public class FbmSiteBaseExcelParam {
    @Excel(name = "id")
    @ApiModelProperty(value = "平台id")
    String id;
    /**
     * 平台名称，小网站为host
     */
    @Excel(name = "平台名称")
    @ApiModelProperty(value = "平台名称")
    String siteShowName;
    @Excel(name = "一级域名")
    @ApiModelProperty(value = "一级域名")
    String domain;
    @Excel(name = "二级域名")
    @ApiModelProperty(value = "二级域名")
    String host;
    @Excel(name = "平台类型")
    @ApiModelProperty(value = "平台类型")
    String siteType;
    @Excel(name = "内容一级类型")
    @ApiModelProperty(value = "内容一级类型")
    String contentFirstCategory;
    @Excel(name = "内容二级类型")
    @ApiModelProperty(value = "内容二级类型")
    String contentSecondCategory;


    @Excel(name = "联系人")
    @ApiModelProperty(value = "联系人")
    String contactName;
    @Excel(name = "联系人邮箱")
    @ApiModelProperty(value = "联系人邮箱")
    String contactEmail;
    @Excel(name = "联系人手机")
    @ApiModelProperty(value = "联系人手机")
    String contactMobile;


    @Excel(name = "ICP备案号")
    @ApiModelProperty(value = "ICP备案号")
    String icpNum;
    @Excel(name = "ICP备案单位名称")
    @ApiModelProperty(value = "ICP备案单位名称")
    String icpOrgName;
    @Excel(name = "ICP备案单位性质")
    @ApiModelProperty(value = "ICP备案单位性质")
    String icpOrgNature;
    @Excel(name = "域名解析服务器地址")
    @ApiModelProperty(value = "域名解析服务器地址")
    String domainServer;
}
