package org.jeecg.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.util.List;

/**
 * @Author: ggr
 * @CreateTime: 2025-09-04
 * @Description:
 * @Version: 1.0
 */

@Data
public class FbmWorksVo {
    @ApiModelProperty("作品id")
    @Excel(name = "作品id")
    private Long id;
    @ApiModelProperty("作品名称")
    @Excel(name = "作品名称")
    private String worksName;
    @ApiModelProperty("作品类型")
    @Excel(name = "作品类型")
    private String worksType;
    @ApiModelProperty("独家权利(0独家 1非独家)")
    @Excel(name = "独家权利(0独家 1非独家)")
    private String exclusiveRights;
    @ApiModelProperty("对接人")
    @Excel(name = "对接人")
    private String contactPerson;
    @ApiModelProperty("内容级别")
    @Excel(name = "内容级别")
    private String contentRating;
    @ApiModelProperty("播出平台")
    @Excel(name = "播出平台")
    private String broadcastPlatform;
    @ApiModelProperty("客户名称")
    @Excel(name = "客户名称")
    private String customerName;
    @ApiModelProperty("维权情况 0：常规 不维权 1：需要维权',")
    @Excel(name = "维权情况 0：常规 不维权 1：需要维权',")
    private String rightsStatus;
    @ApiModelProperty("监测开始时间")
    @Excel(name = "监测开始时间")
    private String monitorStartTime;
    @ApiModelProperty("监测结束时间")
    @Excel(name = "监测结束时间")
    private String monitorEndTime;
    @ApiModelProperty("重点关注平台")
    @Excel(name = "重点关注平台")
    private List<String> siteList;
    @ApiModelProperty("场次")
    @Excel(name = "场次")
    private String scene;
    //一级分类
    @ApiModelProperty("一级分类")
    @Excel(name = "一级分类")
    private String firstCategory;
    //二级分类
    @ApiModelProperty("二级分类")
    @Excel(name = "二级分类")
    private String secondCategory;


}
