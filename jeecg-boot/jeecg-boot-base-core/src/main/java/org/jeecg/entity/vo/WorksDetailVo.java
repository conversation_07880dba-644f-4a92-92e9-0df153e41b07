package org.jeecg.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecg.entity.po.InfringementClues;
import org.jeecg.entity.po.PowerMaterial;
import org.jeecg.entity.po.white.FbmMiguWhite;

import java.util.List;

/**
 * @Author: ggr
 * @CreateTime: 2025-09-04
 * @Description:
 * @Version: 1.0
 */

@Data
public class WorksDetailVo {
    //作品基本信息
    @ApiModelProperty("作品id")
    private Long id;
    @ApiModelProperty("作品名称")
    private String worksName;
    @ApiModelProperty("作品关键词")
    private String keywords;
    @ApiModelProperty("导演")
    private String director;
    @ApiModelProperty("演员")
    private String actors;
    @ApiModelProperty("主页链接")
    private String homepageLink;
    @ApiModelProperty("作品一级类型")
    private String firstCategory;
    @ApiModelProperty("作品二级类型")
    private String secondCategory;
    @ApiModelProperty("监测开始时间")
    private String rightsStartTime;
    @ApiModelProperty("监测结束时间")
    private String rightsEndTime;
    @ApiModelProperty("内容评级")
    private String contentRating;
    @ApiModelProperty("播出平台")
    private String broadcastPlatform;
    @ApiModelProperty("对接人")
    private String contactPerson;
    @ApiModelProperty("需求方公司")
    private String customerName;
    @ApiModelProperty("重点关注平台集合")
    private List<String> siteList;
    //侵权线索信息
    //总线索量
    @ApiModelProperty("总线索量")
    Long tortCountAll;
    @ApiModelProperty("有效线索数量")
    //有效线索数量
    Long validTortCount;
    //无效线索数量
    @ApiModelProperty("无效线索数量")
    Long invalidTortCount;
    @ApiModelProperty("侵权线索信息")
    private List<InfringementClues> infringementClues;

    //权力材料信息
    @ApiModelProperty("权力材料信息")
    private List<PowerMaterial> powerMaterial;

    //白名单信息
    @ApiModelProperty("白名单信息")
    private List<FbmMiguWhite> miguWhite;


}
