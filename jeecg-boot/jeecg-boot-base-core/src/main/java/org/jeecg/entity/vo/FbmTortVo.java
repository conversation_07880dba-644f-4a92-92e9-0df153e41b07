package org.jeecg.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author: ggr
 * @CreateTime: 2025-09-09
 * @Description:
 * @Version: 1.0
 */
@Slf4j
@Data
public class FbmTortVo {
    @ApiModelProperty(value = "作品id")
    private String worksId;
    @ApiModelProperty(value = "侵权标题")
    private String title;
    @ApiModelProperty(value = "侵权链接")
    private String url;
    @ApiModelProperty(value = "数据发布时间")
    private String dataReleaseTime;
    @ApiModelProperty("发布者")
    private String publisher;
    @ApiModelProperty("侵权平台")
    private String siteShowName;
    @ApiModelProperty("发函时间")
    private String tortFirstSendMailTime;



}
