package org.jeecg.entity.po;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * @Description: 作品线索数量
 * @Author: jeecg-boot
 * @Date: 2025-09-05
 * @Version: V1.0
 */
@Data
@TableName("works_detail_tort_count")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "works_detail_tort_count对象", description = "作品线索数量")
public class WorksDetailTortCount {

    /**
     * 作品id
     */
    @Excel(name = "作品id", width = 15)
    @ApiModelProperty(value = "作品id")
    private java.lang.Integer worksId;
    /**
     * 总线索数
     */
    @Excel(name = "总线索数", width = 15)
    @ApiModelProperty(value = "总线索数")
    private java.lang.String tortCount;
    /**
     * 有效线索数
     */
    @Excel(name = "有效线索数", width = 15)
    @ApiModelProperty(value = "有效线索数")
    private java.lang.String tortValiCount;
    /**
     * 无效线索数
     */
    @Excel(name = "无效线索数", width = 15)
    @ApiModelProperty(value = "无效线索数")
    private java.lang.String tortInvalidCount;
}
