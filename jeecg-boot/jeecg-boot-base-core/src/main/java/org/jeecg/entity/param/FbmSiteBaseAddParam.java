package org.jeecg.entity.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author: ggr
 * @CreateTime: 2025-09-01
 * @Description:
 * @Version: 1.0
 */

@Data
@Slf4j
public class FbmSiteBaseAddParam {
    /**平台名称，小网站为host*/
    @ApiModelProperty(value = "平台名称")
   String siteShowName;
    @ApiModelProperty(value = "一级域名")
    String domain;
    @ApiModelProperty(value = "二级域名")
    String host;
    @ApiModelProperty(value = "平台类型")
    String siteType;
    @ApiModelProperty(value = "内容一级类型")
    String contentFirstCategory;
    @ApiModelProperty(value = "内容二级类型")
    String contentSecondCategory;




    @ApiModelProperty(value = "联系人")
    String contactName;
    @ApiModelProperty(value = "联系人邮箱")
    String contactEmail;
    @ApiModelProperty(value = "联系人手机")
    String contactMobile;



    @ApiModelProperty(value = "ICP备案号")
    String icpNum;
    @ApiModelProperty(value = "ICP备案单位名称")
    String icpOrgName;
    @ApiModelProperty(value = "ICP备案单位性质")
    String icpOrgNature;
    @ApiModelProperty(value = "域名解析服务器地址")
    String domainServer;
}
